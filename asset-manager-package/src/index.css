@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent;
  }
  
  .card-hover {
    @apply hover:shadow-xl hover:scale-105 transition-all duration-300;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-purple-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-purple-400;
}

/* Smooth animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}