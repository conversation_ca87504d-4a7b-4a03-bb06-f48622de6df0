#!/bin/bash

echo "🚀 Setting up Asset Manager on macOS..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script in the project directory with package.json"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Set up Tailwind CSS
echo "🎨 Setting up Tailwind CSS..."
npx tailwindcss init -p

# Build the application
echo "🔨 Building the application..."
npm run build

# Start the development server
echo "✅ Setup complete! Starting development server..."
echo "🌐 Your application will be available at: http://localhost:5173"
echo "🔑 Login credentials:"
echo "   Username: admin"
echo "   Password: password"
echo ""
echo "Press Ctrl+C to stop the server"

npm run dev