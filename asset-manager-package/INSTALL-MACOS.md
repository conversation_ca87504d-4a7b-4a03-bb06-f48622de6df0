# 🍎 Asset Manager - MacBook Pro M1 Installation Guide

## Quick Start (Easiest Method)

### Step 1: Download Required Files
You'll need these files from this project:
- All files from the `dist` folder (after building)
- OR all source files if you want the development version

### Step 2: Choose Your Installation Method

## Method A: Static Files (Recommended for Testing)

1. **Download the `dist` folder contents** after the build completes
2. **Open Terminal** on your MacBook
3. **Navigate to the downloaded folder**:
   ```bash
   cd ~/Downloads/dist  # or wherever you saved the files
   ```
4. **Start a simple web server**:
   ```bash
   python3 -m http.server 8080
   ```
5. **Open your browser** and go to: `http://localhost:8080`

## Method B: Full Development Setup

1. **Install Node.js** (if not installed):
   ```bash
   # Install Homebrew first
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install Node.js
   brew install node
   ```

2. **Create project folder**:
   ```bash
   mkdir ~/asset-manager
   cd ~/asset-manager
   ```

3. **Copy all project files** to this folder

4. **Install dependencies and run**:
   ```bash
   npm install
   npm run build
   npm run dev
   ```

## Login Information
- **Username:** `admin`
- **Password:** `password`

## What You Get
✅ Complete Asset Management System  
✅ User & Role Management  
✅ Location Management  
✅ Asset Check-in/Check-out  
✅ Reporting & Analytics  
✅ AI-Powered Queries  
✅ Bulk Import/Export  
✅ Mad Assemblage Studios Branding  

## Troubleshooting

**Can't find dist folder?**
- Run `npm run build` first to create it

**Port 8080 in use?**
```bash
python3 -m http.server 3000
# Then visit http://localhost:3000
```

**Need to stop the server?**
- Press `Ctrl + C` in Terminal

Your Asset Manager will be fully functional on your MacBook Pro M1!