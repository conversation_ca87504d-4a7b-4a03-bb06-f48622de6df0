#!/bin/bash

echo "📦 Creating downloadable package for MacBook Pro M1..."

# Create package directory
mkdir -p asset-manager-package

# Copy essential files
cp -r dist asset-manager-package/ 2>/dev/null || echo "⚠️  dist folder not found - run 'npm run build' first"
cp package.json asset-manager-package/
cp vite.config.ts asset-manager-package/
cp tailwind.config.js asset-manager-package/
cp postcss.config.js asset-manager-package/
cp tsconfig.json asset-manager-package/
cp tsconfig.app.json asset-manager-package/
cp tsconfig.node.json asset-manager-package/
cp index.html asset-manager-package/
cp -r src asset-manager-package/
cp INSTALL-MACOS.md asset-manager-package/
cp setup-macos.sh asset-manager-package/

# Make setup script executable
chmod +x asset-manager-package/setup-macos.sh

echo "✅ Package created in 'asset-manager-package' folder"
echo "📁 You can now download this entire folder to your MacBook"
echo ""
echo "🚀 On your MacBook, run:"
echo "   cd asset-manager-package"
echo "   ./setup-macos.sh"
echo ""
echo "🌐 Or for quick testing:"
echo "   cd asset-manager-package/dist"
echo "   python3 -m http.server 8080"