{"name": "asset-manager-assemblage", "version": "1.0.0", "description": "Asset Management System for Assemblage Studios", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "python3 -m http.server 8080"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^5.4.2"}}