import React from 'react';
import { useAuthContext } from './AuthProvider';
import { useAssets } from '../hooks/useAssets';

const DebugInfo: React.FC = () => {
  const { user, hasPermission } = useAuthContext();
  const { assets } = useAssets();

  if (!user) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-300 rounded-lg p-4 max-w-sm">
        <h3 className="font-bold text-red-800">Debug: Not Logged In</h3>
        <p className="text-red-600">User is not authenticated</p>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-300 rounded-lg p-4 max-w-sm text-xs">
      <h3 className="font-bold text-blue-800 mb-2">Debug Info</h3>
      <div className="space-y-1 text-blue-700">
        <p><strong>User:</strong> {user.username}</p>
        <p><strong>Role:</strong> {user.role.name}</p>
        <p><strong>Can Edit Assets:</strong> {hasPermission('assets', 'update') ? 'Yes' : 'No'}</p>
        <p><strong>Assets Count:</strong> {assets.length}</p>
        <p><strong>Active Assets:</strong> {assets.filter(a => a.status !== 'Retired').length}</p>
      </div>
    </div>
  );
};

export default DebugInfo;
