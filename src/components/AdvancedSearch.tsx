import React, { useState } from 'react';
import { Search, Filter, Calendar, X, ChevronDown, ChevronUp, MapPin } from 'lucide-react';

interface AdvancedSearchProps {
  onFiltersChange: (filters: SearchFilters) => void;
  vendors: string[];
  models: string[];
  assignedUsers: string[];
  locations: string[];
}

export interface SearchFilters {
  searchTerm: string;
  statusFilter: string;
  typeFilter: string;
  acquisitionFilter: string;
  vendorFilter: string;
  modelFilter: string;
  assignedToFilter: string;
  locationFilter: string;
  specificationsSearch: string;
  dateField: string;
  dateRangeStart: string;
  dateRangeEnd: string;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  onFiltersChange,
  vendors,
  models,
  assignedUsers,
  locations
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: '',
    statusFilter: 'All',
    typeFilter: 'All',
    acquisitionFilter: 'All',
    vendorFilter: 'All',
    modelFilter: 'All',
    assignedToFilter: 'All',
    locationFilter: 'All',
    specificationsSearch: '',
    dateField: 'purchaseDate',
    dateRangeStart: '',
    dateRangeEnd: ''
  });

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters: SearchFilters = {
      searchTerm: '',
      statusFilter: 'All',
      typeFilter: 'All',
      acquisitionFilter: 'All',
      vendorFilter: 'All',
      modelFilter: 'All',
      assignedToFilter: 'All',
      locationFilter: 'All',
      specificationsSearch: '',
      dateField: 'purchaseDate',
      dateRangeStart: '',
      dateRangeEnd: ''
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = () => {
    return filters.searchTerm !== '' ||
           filters.statusFilter !== 'All' ||
           filters.typeFilter !== 'All' ||
           filters.acquisitionFilter !== 'All' ||
           filters.vendorFilter !== 'All' ||
           filters.modelFilter !== 'All' ||
           filters.assignedToFilter !== 'All' ||
           filters.locationFilter !== 'All' ||
           filters.specificationsSearch !== '' ||
           filters.dateRangeStart !== '' ||
           filters.dateRangeEnd !== '';
  };

  const dateFieldOptions = [
    { value: 'purchaseDate', label: 'Purchase Date' },
    { value: 'warrantyExpiry', label: 'Warranty Expiry' },
    { value: 'lastMaintenance', label: 'Last Maintenance' },
    { value: 'nextMaintenance', label: 'Next Maintenance' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Updated Date' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Basic Search Row */}
      <div className="flex flex-col lg:flex-row gap-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search assets by name, serial number, or model..."
            value={filters.searchTerm}
            onChange={(e) => updateFilters({ searchTerm: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div className="flex gap-3">
          <select
            value={filters.acquisitionFilter}
            onChange={(e) => updateFilters({ acquisitionFilter: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="All">All Assets</option>
            <option value="Purchased">Purchased</option>
            <option value="Rental">Rental</option>
          </select>
          
          <select
            value={filters.statusFilter}
            onChange={(e) => updateFilters({ statusFilter: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="All">All Status</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Maintenance">Maintenance</option>
            <option value="Retired">Retired</option>
            <option value="Checked Out">Checked Out</option>
          </select>
          
          <select
            value={filters.typeFilter}
            onChange={(e) => updateFilters({ typeFilter: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="All">All Types</option>
            <option value="Hardware">Hardware</option>
            <option value="Software">Software</option>
            <option value="Equipment">Equipment</option>
            <option value="Furniture">Furniture</option>
            <option value="Vehicle">Vehicle</option>
          </select>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Filter className="w-4 h-4" />
            Advanced
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Specifications Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Specifications Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search in descriptions, categories, specifications..."
                value={filters.specificationsSearch}
                onChange={(e) => updateFilters({ specificationsSearch: e.target.value })}
                className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Vendor, Model, Assigned User, and Location Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Vendor</label>
              <select
                value={filters.vendorFilter}
                onChange={(e) => updateFilters({ vendorFilter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="All">All Vendors</option>
                {vendors.map((vendor) => (
                  <option key={vendor} value={vendor}>{vendor}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Model</label>
              <select
                value={filters.modelFilter}
                onChange={(e) => updateFilters({ modelFilter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="All">All Models</option>
                {models.map((model) => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
              <select
                value={filters.assignedToFilter}
                onChange={(e) => updateFilters({ assignedToFilter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="All">All Users</option>
                {assignedUsers.map((user) => (
                  <option key={user} value={user}>{user}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                Location
              </label>
              <select
                value={filters.locationFilter}
                onChange={(e) => updateFilters({ locationFilter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="All">All Locations</option>
                {locations.map((location) => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range Filter
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Date Field</label>
                <select
                  value={filters.dateField}
                  onChange={(e) => updateFilters({ dateField: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {dateFieldOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs text-gray-500 mb-1">From Date</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="date"
                    value={filters.dateRangeStart}
                    onChange={(e) => updateFilters({ dateRangeStart: e.target.value })}
                    className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-xs text-gray-500 mb-1">To Date</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="date"
                    value={filters.dateRangeEnd}
                    onChange={(e) => updateFilters({ dateRangeEnd: e.target.value })}
                    className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters() && (
            <div className="flex justify-end pt-2">
              <button
                onClick={clearAllFilters}
                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-4 h-4" />
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters() && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600 font-medium">Active filters:</span>
            {filters.searchTerm && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Search: "{filters.searchTerm}"
                <button onClick={() => updateFilters({ searchTerm: '' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.statusFilter !== 'All' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Status: {filters.statusFilter}
                <button onClick={() => updateFilters({ statusFilter: 'All' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.locationFilter !== 'All' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                <MapPin className="w-3 h-3" />
                Location: {filters.locationFilter}
                <button onClick={() => updateFilters({ locationFilter: 'All' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.vendorFilter !== 'All' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Vendor: {filters.vendorFilter}
                <button onClick={() => updateFilters({ vendorFilter: 'All' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.specificationsSearch && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                Specs: "{filters.specificationsSearch}"
                <button onClick={() => updateFilters({ specificationsSearch: '' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {(filters.dateRangeStart || filters.dateRangeEnd) && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                Date Range
                <button onClick={() => updateFilters({ dateRangeStart: '', dateRangeEnd: '' })}>
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedSearch;