import React, { useState } from 'react';
import { X, Clock, User, Edit, Eye, Download, Filter } from 'lucide-react';
import { useAuditTrail } from '../hooks/useAuditTrail';
import { AuditLogEntry, AssetHistoryFilter } from '../types/AuditTrail';

interface AssetHistoryModalProps {
  assetId: string;
  assetName: string;
  isOpen: boolean;
  onClose: () => void;
}

const AssetHistoryModal: React.FC<AssetHistoryModalProps> = ({
  assetId,
  assetName,
  isOpen,
  onClose
}) => {
  const { getAssetHistory, getFilteredHistory, exportAuditLog } = useAuditTrail();
  const [filter, setFilter] = useState<AssetHistoryFilter>({ assetId });
  const [showFilters, setShowFilters] = useState(false);

  if (!isOpen) return null;

  const history = getFilteredHistory(filter);

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created': return <div className="w-2 h-2 bg-green-500 rounded-full" />;
      case 'updated': return <Edit className="w-4 h-4 text-blue-600" />;
      case 'retired': return <div className="w-2 h-2 bg-red-500 rounded-full" />;
      case 'checked_in': return <div className="w-2 h-2 bg-green-500 rounded-full" />;
      case 'checked_out': return <div className="w-2 h-2 bg-orange-500 rounded-full" />;
      case 'status_changed': return <div className="w-2 h-2 bg-purple-500 rounded-full" />;
      case 'assigned': return <User className="w-4 h-4 text-blue-600" />;
      case 'unassigned': return <User className="w-4 h-4 text-gray-400" />;
      default: return <div className="w-2 h-2 bg-gray-400 rounded-full" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created': return 'text-green-700 bg-green-50 border-green-200';
      case 'updated': return 'text-blue-700 bg-blue-50 border-blue-200';
      case 'retired': return 'text-red-700 bg-red-50 border-red-200';
      case 'checked_in': return 'text-green-700 bg-green-50 border-green-200';
      case 'checked_out': return 'text-orange-700 bg-orange-50 border-orange-200';
      case 'status_changed': return 'text-purple-700 bg-purple-50 border-purple-200';
      case 'assigned': return 'text-blue-700 bg-blue-50 border-blue-200';
      case 'unassigned': return 'text-gray-700 bg-gray-50 border-gray-200';
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const formatActionText = (entry: AuditLogEntry) => {
    switch (entry.action) {
      case 'created':
        return 'Asset was created';
      case 'updated':
        if (entry.field) {
          return `Updated ${entry.field}: "${entry.oldValue}" → "${entry.newValue}"`;
        }
        return 'Asset was updated';
      case 'retired':
        return 'Asset was retired';
      case 'checked_in':
        return 'Asset was checked in';
      case 'checked_out':
        return 'Asset was checked out';
      case 'status_changed':
        return `Status changed: "${entry.oldValue}" → "${entry.newValue}"`;
      case 'assigned':
        return `Assigned to: ${entry.newValue}`;
      case 'unassigned':
        return `Unassigned from: ${entry.oldValue}`;
      default:
        return entry.action.replace('_', ' ');
    }
  };

  const handleExport = () => {
    exportAuditLog(filter);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Clock className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Asset History</h2>
              <p className="text-sm text-gray-600">{assetName}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              Filters
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
                <select
                  value={filter.action || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, action: e.target.value || undefined }))}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Actions</option>
                  <option value="created">Created</option>
                  <option value="updated">Updated</option>
                  <option value="retired">Retired</option>
                  <option value="checked_in">Checked In</option>
                  <option value="checked_out">Checked Out</option>
                  <option value="status_changed">Status Changed</option>
                  <option value="assigned">Assigned</option>
                  <option value="unassigned">Unassigned</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input
                  type="date"
                  value={filter.dateFrom || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, dateFrom: e.target.value || undefined }))}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input
                  type="date"
                  value={filter.dateTo || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, dateTo: e.target.value || undefined }))}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => setFilter({ assetId })}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {history.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No History Found</h3>
              <p className="text-gray-600">No audit trail entries match your current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry, index) => (
                <div key={entry.id} className="relative">
                  {/* Timeline line */}
                  {index < history.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
                  )}
                  
                  <div className="flex items-start gap-4">
                    {/* Timeline dot */}
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-white border-2 border-gray-200 flex-shrink-0">
                      {getActionIcon(entry.action)}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className={`p-4 rounded-lg border ${getActionColor(entry.action)}`}>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">
                              {formatActionText(entry)}
                            </p>
                            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <User className="w-4 h-4" />
                                <span>{entry.userName}</span>
                                <span className="text-gray-400">({entry.userRole})</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                <span>{new Date(entry.timestamp).toLocaleString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {entry.notes && (
                          <div className="mt-3 p-3 bg-white bg-opacity-50 rounded border border-gray-200">
                            <p className="text-sm text-gray-700">
                              <span className="font-medium">Notes:</span> {entry.notes}
                            </p>
                          </div>
                        )}
                        
                        {entry.metadata && Object.keys(entry.metadata).length > 0 && (
                          <div className="mt-3 p-3 bg-white bg-opacity-50 rounded border border-gray-200">
                            <p className="text-sm font-medium text-gray-700 mb-2">Additional Details:</p>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              {Object.entries(entry.metadata).map(([key, value]) => (
                                <div key={key}>
                                  <span className="font-medium text-gray-600">{key}:</span>
                                  <span className="ml-1 text-gray-700">{String(value)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{history.length} entries found</span>
            <span>Showing audit trail for {assetName}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetHistoryModal;