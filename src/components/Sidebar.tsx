import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuthContext } from './AuthProvider';
import { 
  Home, 
  Plus, 
  Edit, 
  Trash2, 
  ArrowRightLeft, 
  FileText, 
  Bot,
  Package,
  Upload,
  Users,
  LogOut,
  User,
  MapPin,
  Shield
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { user, logout, hasPermission } = useAuthContext();

  const navItems = [
    { to: '/', icon: Home, label: 'Dashboard', permission: null },
    { to: '/assets', icon: Package, label: 'All Assets', permission: { resource: 'assets', action: 'read' } },
    { to: '/add-asset', icon: Plus, label: 'Add Asset', permission: { resource: 'assets', action: 'create' } },
    { to: '/edit-asset', icon: Edit, label: 'Edit Asset', permission: { resource: 'assets', action: 'update' } },
    { to: '/retire-asset', icon: Trash2, label: 'Retire Asset', permission: { resource: 'assets', action: 'delete' } },
    { to: '/check-in-out', icon: ArrowRightLeft, label: 'Check In/Out', permission: { resource: 'assets', action: 'update' } },
    { to: '/bulk-import-export', icon: Upload, label: 'Bulk Import/Export', permission: { resource: 'bulk-operations', action: 'import' } },
    { to: '/reports', icon: FileText, label: 'Reports', permission: { resource: 'reports', action: 'read' } },
    { to: '/audit-trail', icon: Shield, label: 'Audit Trail', permission: { resource: 'reports', action: 'read' } },
    { to: '/ai-query', icon: Bot, label: 'AI Query', permission: { resource: 'assets', action: 'read' } },
    { to: '/user-management', icon: Users, label: 'User Management', permission: { resource: 'users', action: 'read' } },
    { to: '/location-management', icon: MapPin, label: 'Location Management', permission: { resource: 'locations', action: 'read' } }
  ];

  const visibleNavItems = navItems.filter(item => {
    if (!item.permission) return true;
    return hasPermission(item.permission.resource, item.permission.action);
  });

  return (
    <div className="w-64 bg-gradient-to-b from-slate-900 to-slate-800 shadow-2xl h-full border-r border-purple-500/20 flex flex-col">
      <div className="p-6 border-b border-purple-500/20">
        <div className="flex items-center gap-3 mb-2">
          <div className="h-10 w-10 flex items-center justify-center">
            <img 
              src="https://www.madassemblage.com/wp-content/uploads/2022/09/logo.png" 
              alt="Mad Assemblage Logo"
              className="h-10 w-auto object-contain"
              onError={(e) => {
                // Fallback to text logo if image fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="hidden h-10 w-10 flex items-center justify-center rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
              <div className="text-white font-bold text-sm">MA</div>
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">Asset Manager</h1>
            <p className="text-xs text-purple-200">Assemblage Studios</p>
          </div>
        </div>
      </div>

      {/* User Info */}
      {user && (
        <div className="p-4 border-b border-purple-500/20 bg-gradient-to-r from-purple-900/50 to-pink-900/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600/30 rounded-full backdrop-blur-sm">
              <User className="w-4 h-4 text-purple-200" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-purple-200 truncate">{user.role.name}</p>
              <p className="text-xs text-purple-300 truncate">{user.department} • {user.location}</p>
            </div>
          </div>
        </div>
      )}
      
      <nav className="flex-1 mt-4">
        {visibleNavItems.map((item) => (
          <NavLink
            key={item.to}
            to={item.to}
            className={({ isActive }) =>
              `flex items-center gap-3 px-6 py-3 text-sm font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-gradient-to-r from-purple-600/30 to-pink-600/30 text-white border-r-2 border-purple-400 backdrop-blur-sm'
                  : 'text-purple-200 hover:bg-white/10 hover:text-white'
              }`
            }
          >
            <item.icon className="w-5 h-5" />
            {item.label}
          </NavLink>
        ))}
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-purple-500/20">
        <button
          onClick={logout}
          className="w-full flex items-center gap-3 px-4 py-2 text-sm font-medium text-purple-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200"
        >
          <LogOut className="w-5 h-5" />
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default Sidebar;