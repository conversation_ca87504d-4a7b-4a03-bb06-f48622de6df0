export interface Asset {
  id: string;
  name: string;
  type: 'Hardware' | 'Software' | 'Equipment' | 'Furniture' | 'Vehicle';
  category: string;
  serialNumber: string;
  model: string;
  manufacturer: string;
  purchaseDate: string;
  purchasePrice: number;
  currentValue: number;
  status: 'Active' | 'Inactive' | 'Maintenance' | 'Retired' | 'Checked Out';
  location: string;
  assignedTo: string;
  description: string;
  warrantyExpiry: string;
  lastMaintenance: string;
  nextMaintenance: string;
  vendor: string;
  isRental: boolean;
  rentalDetails?: {
    startDate: string;
    endDate: string;
    cost: number;
    contactPerson: string;
    contactEmail: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CheckInOutRecord {
  id: string;
  assetId: string;
  assetName: string;
  action: 'Check In' | 'Check Out';
  date: string;
  person: string;
  vendor: string;
  notes: string;
  dueDate?: string;
}

interface AssetReport {
  totalAssets: number;
  activeAssets: number;
  retiredAssets: number;
  totalValue: number;
  assetsInMaintenance: number;
  rentedAssets: number;
  assetsByType: Record<string, number>;
  assetsByLocation: Record<string, number>;
  upcomingMaintenances: Asset[];
  expiringWarranties: Asset[];
}