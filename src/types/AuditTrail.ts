export interface AuditLogEntry {
  id: string;
  assetId: string;
  assetName: string;
  action: 'created' | 'updated' | 'retired' | 'checked_in' | 'checked_out' | 'status_changed' | 'assigned' | 'unassigned';
  field?: string; // Which field was changed (for updates)
  oldValue?: string | number | boolean;
  newValue?: string | number | boolean;
  userId: string;
  userName: string;
  userRole: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface AssetHistoryFilter {
  assetId?: string;
  userId?: string;
  action?: string;
  dateFrom?: string;
  dateTo?: string;
  field?: string;
}

export interface AuditTrailSummary {
  totalEntries: number;
  uniqueAssets: number;
  uniqueUsers: number;
  actionCounts: Record<string, number>;
  recentActivity: AuditLogEntry[];
  topUsers: Array<{ userId: string; userName: string; count: number }>;
}