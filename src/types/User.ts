export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department: string;
  location: string;
  isActive: boolean;
  password: string;
  passwordResetRequired: boolean;
  lastPasswordReset?: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}

export interface UserRole {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  departmentRestrictions: string[];
  locationRestrictions: string[];
  isSystemRole: boolean;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: 'assets' | 'users' | 'reports' | 'settings' | 'bulk-operations' | 'locations';
  action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import';
  scope: 'all' | 'department' | 'location' | 'own';
}

export interface AuthContext {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasPermission: (resource: string, action: string, targetDepartment?: string, targetLocation?: string) => boolean;
  canAccessAsset: (asset: any) => boolean;
  resetUserPassword: (userId: string, newPassword: string, requireChange?: boolean) => void;
}