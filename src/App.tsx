import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider, useAuthContext } from './components/AuthProvider';
import LoginForm from './components/LoginForm';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import AllAssets from './pages/AllAssets';
import AddAsset from './pages/AddAsset';
import EditAsset from './pages/EditAsset';
import RetireAsset from './pages/RetireAsset';
import CheckInOut from './pages/CheckInOut';
import Reports from './pages/Reports';
import AIQuery from './pages/AIQuery';
import BulkImportExport from './pages/BulkImportExport';
import UserManagement from './pages/UserManagement';
import LocationManagement from './pages/LocationManagement';
import AuditTrail from './pages/AuditTrail';

const AppContent: React.FC = () => {
  const { user } = useAuthContext();

  if (!user) {
    return <LoginForm />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route 
            path="assets" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'read' }}>
                <AllAssets />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="add-asset" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'create' }}>
                <AddAsset />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="edit-asset" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'update' }}>
                <EditAsset />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="retire-asset" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'delete' }}>
                <RetireAsset />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="check-in-out" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'update' }}>
                <CheckInOut />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="bulk-import-export" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'bulk-operations', action: 'import' }}>
                <BulkImportExport />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="reports" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'reports', action: 'read' }}>
                <Reports />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="ai-query" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'assets', action: 'read' }}>
                <AIQuery />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="user-management" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'users', action: 'read' }}>
                <UserManagement />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="location-management" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'locations', action: 'read' }}>
                <LocationManagement />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="audit-trail" 
            element={
              <ProtectedRoute requiredPermission={{ resource: 'reports', action: 'read' }}>
                <AuditTrail />
              </ProtectedRoute>
            } 
          />
        </Route>
      </Routes>
    </Router>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;