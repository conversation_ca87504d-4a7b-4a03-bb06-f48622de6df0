import { useState, useEffect, createContext, useContext } from 'react';
import { User, UserRole, Permission, AuthContext } from '../types/User';

const USERS_STORAGE_KEY = 'asset-inventory-users';
const ROLES_STORAGE_KEY = 'asset-inventory-roles';
const LOCATIONS_STORAGE_KEY = 'asset-inventory-locations';
const CURRENT_USER_KEY = 'asset-inventory-current-user';

// Default system roles and permissions
const defaultPermissions: Permission[] = [
  // Asset permissions
  { id: 'asset-create', name: 'Create Assets', description: 'Create new assets', resource: 'assets', action: 'create', scope: 'all' },
  { id: 'asset-read', name: 'View Assets', description: 'View asset information', resource: 'assets', action: 'read', scope: 'all' },
  { id: 'asset-update', name: 'Edit Assets', description: 'Edit asset information', resource: 'assets', action: 'update', scope: 'all' },
  { id: 'asset-delete', name: 'Retire Assets', description: 'Retire assets', resource: 'assets', action: 'delete', scope: 'all' },
  { id: 'asset-read-dept', name: 'View Department Assets', description: 'View assets in same department', resource: 'assets', action: 'read', scope: 'department' },
  { id: 'asset-update-dept', name: 'Edit Department Assets', description: 'Edit assets in same department', resource: 'assets', action: 'update', scope: 'department' },
  { id: 'asset-read-location', name: 'View Location Assets', description: 'View assets in same location', resource: 'assets', action: 'read', scope: 'location' },
  
  // Bulk operations
  { id: 'bulk-import', name: 'Import Assets', description: 'Import assets from files', resource: 'bulk-operations', action: 'import', scope: 'all' },
  { id: 'bulk-export', name: 'Export Assets', description: 'Export asset data', resource: 'bulk-operations', action: 'export', scope: 'all' },
  
  // Reports
  { id: 'reports-view', name: 'View Reports', description: 'View asset reports', resource: 'reports', action: 'read', scope: 'all' },
  { id: 'reports-export', name: 'Export Reports', description: 'Export report data', resource: 'reports', action: 'export', scope: 'all' },
  
  // User management
  { id: 'users-create', name: 'Create Users', description: 'Create new users', resource: 'users', action: 'create', scope: 'all' },
  { id: 'users-read', name: 'View Users', description: 'View user information', resource: 'users', action: 'read', scope: 'all' },
  { id: 'users-update', name: 'Edit Users', description: 'Edit user information', resource: 'users', action: 'update', scope: 'all' },
  { id: 'users-delete', name: 'Delete Users', description: 'Delete users', resource: 'users', action: 'delete', scope: 'all' },
  
  // Location management
  { id: 'locations-create', name: 'Create Locations', description: 'Create new locations', resource: 'locations', action: 'create', scope: 'all' },
  { id: 'locations-read', name: 'View Locations', description: 'View location information', resource: 'locations', action: 'read', scope: 'all' },
  { id: 'locations-update', name: 'Edit Locations', description: 'Edit location information', resource: 'locations', action: 'update', scope: 'all' },
  { id: 'locations-delete', name: 'Delete Locations', description: 'Delete locations', resource: 'locations', action: 'delete', scope: 'all' },
  
  // Settings
  { id: 'settings-manage', name: 'Manage Settings', description: 'Manage system settings', resource: 'settings', action: 'update', scope: 'all' }
];

const defaultRoles: UserRole[] = [
  {
    id: 'super-admin',
    name: 'Super Administrator',
    description: 'Full system access with all permissions',
    permissions: defaultPermissions,
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  },
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Administrative access with user management',
    permissions: defaultPermissions.filter(p => p.resource !== 'settings'),
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  },
  {
    id: 'manager',
    name: 'Department Manager',
    description: 'Manage assets within department',
    permissions: [
      ...defaultPermissions.filter(p => p.resource === 'assets' && ['create', 'read', 'update', 'delete'].includes(p.action) && ['all', 'department'].includes(p.scope)),
      ...defaultPermissions.filter(p => p.resource === 'reports' && p.action === 'read'),
      ...defaultPermissions.filter(p => p.resource === 'bulk-operations'),
      ...defaultPermissions.filter(p => p.resource === 'locations' && ['read', 'update'].includes(p.action))
    ],
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  },
  {
    id: 'editor',
    name: 'Asset Editor',
    description: 'Edit assets with limited permissions',
    permissions: [
      ...defaultPermissions.filter(p => p.resource === 'assets' && ['read', 'update'].includes(p.action)),
      ...defaultPermissions.filter(p => p.resource === 'reports' && p.action === 'read'),
      ...defaultPermissions.filter(p => p.resource === 'locations' && p.action === 'read')
    ],
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  },
  {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only access to assets',
    permissions: [
      ...defaultPermissions.filter(p => p.resource === 'assets' && p.action === 'read'),
      ...defaultPermissions.filter(p => p.resource === 'reports' && p.action === 'read'),
      ...defaultPermissions.filter(p => p.resource === 'locations' && p.action === 'read')
    ],
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  },
  {
    id: 'location-manager',
    name: 'Location Manager',
    description: 'Manage assets within specific location',
    permissions: [
      ...defaultPermissions.filter(p => p.resource === 'assets' && ['create', 'read', 'update'].includes(p.action) && ['all', 'location'].includes(p.scope)),
      ...defaultPermissions.filter(p => p.resource === 'reports' && p.action === 'read'),
      ...defaultPermissions.filter(p => p.resource === 'locations' && ['read', 'update'].includes(p.action))
    ],
    departmentRestrictions: [],
    locationRestrictions: [],
    isSystemRole: true
  }
];

const defaultUsers: User[] = [
  {
    id: 'admin-001',
    username: 'admin',
    email: '<EMAIL>',
    firstName: 'System',
    lastName: 'Administrator',
    role: defaultRoles[0], // Super Administrator
    department: 'IT',
    location: 'Head Office',
    isActive: true,
    password: 'password', // In production, this would be hashed
    passwordResetRequired: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const defaultLocations = [
  {
    id: 'loc-001',
    name: 'Head Office',
    description: 'Main corporate headquarters',
    address: '123 Business Street, Corporate City',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'loc-002',
    name: 'Animation Studio A',
    description: 'Primary animation production facility',
    address: '456 Creative Avenue, Art District',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'loc-003',
    name: 'Animation Studio B',
    description: 'Secondary animation production facility',
    address: '789 Studio Lane, Creative Quarter',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'loc-004',
    name: 'VFX Studio',
    description: 'Visual effects and post-production facility',
    address: '321 Effects Boulevard, Tech Park',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'loc-005',
    name: 'Equipment Storage',
    description: 'Central equipment storage and maintenance facility',
    address: '654 Storage Drive, Industrial Zone',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'loc-006',
    name: 'Remote',
    description: 'Remote work locations',
    address: 'Various remote locations',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Simple password hashing function (for demo purposes only)
const hashPassword = (password: string): string => {
  // In production, use a proper hashing library like bcrypt
  return btoa(password + 'salt'); // Base64 encoding for demo
};

const verifyPassword = (password: string, hashedPassword: string): boolean => {
  // In production, use proper password verification
  return hashPassword(password) === hashedPassword;
};

export interface Location {
  id: string;
  name: string;
  description: string;
  address: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const useAuth = (): AuthContext & {
  users: User[];
  roles: UserRole[];
  locations: Location[];
  addUser: (user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => User;
  updateUser: (id: string, updates: Partial<User>) => void;
  deleteUser: (id: string) => void;
  addRole: (role: Omit<UserRole, 'id'>) => UserRole;
  updateRole: (id: string, updates: Partial<UserRole>) => void;
  deleteRole: (id: string) => void;
  resetUserPassword: (userId: string, newPassword: string, requireChange?: boolean) => void;
  addLocation: (location: Omit<Location, 'id' | 'createdAt' | 'updatedAt'>) => Location;
  updateLocation: (id: string, updates: Partial<Location>) => void;
  deleteLocation: (id: string) => void;
} => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);

  useEffect(() => {
    // Load users
    const storedUsers = localStorage.getItem(USERS_STORAGE_KEY);
    if (storedUsers) {
      const parsedUsers = JSON.parse(storedUsers);
      // Migrate existing users to include password field
      const migratedUsers = parsedUsers.map((user: any) => ({
        ...user,
        password: user.password || hashPassword('password'),
        passwordResetRequired: user.passwordResetRequired || false
      }));
      setUsers(migratedUsers);
      if (JSON.stringify(migratedUsers) !== storedUsers) {
        localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(migratedUsers));
      }
    } else {
      const usersWithHashedPasswords = defaultUsers.map(user => ({
        ...user,
        password: hashPassword(user.password)
      }));
      setUsers(usersWithHashedPasswords);
      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(usersWithHashedPasswords));
    }

    // Load roles
    const storedRoles = localStorage.getItem(ROLES_STORAGE_KEY);
    if (storedRoles) {
      setRoles(JSON.parse(storedRoles));
    } else {
      setRoles(defaultRoles);
      localStorage.setItem(ROLES_STORAGE_KEY, JSON.stringify(defaultRoles));
    }

    // Load locations
    const storedLocations = localStorage.getItem(LOCATIONS_STORAGE_KEY);
    if (storedLocations) {
      setLocations(JSON.parse(storedLocations));
    } else {
      setLocations(defaultLocations);
      localStorage.setItem(LOCATIONS_STORAGE_KEY, JSON.stringify(defaultLocations));
    }

    // Load current user
    const storedCurrentUser = localStorage.getItem(CURRENT_USER_KEY);
    if (storedCurrentUser) {
      setCurrentUser(JSON.parse(storedCurrentUser));
    }
  }, []);

  const saveUsers = (updatedUsers: User[]) => {
    setUsers(updatedUsers);
    localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(updatedUsers));
  };

  const saveRoles = (updatedRoles: UserRole[]) => {
    setRoles(updatedRoles);
    localStorage.setItem(ROLES_STORAGE_KEY, JSON.stringify(updatedRoles));
  };

  const saveLocations = (updatedLocations: Location[]) => {
    setLocations(updatedLocations);
    localStorage.setItem(LOCATIONS_STORAGE_KEY, JSON.stringify(updatedLocations));
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    // Find user by username
    const user = users.find(u => u.username === username && u.isActive);
    if (!user) return false;

    // Verify password
    const isValidPassword = verifyPassword(password, user.password);
    if (!isValidPassword) return false;

    const updatedUser = { ...user, lastLogin: new Date().toISOString() };
    setCurrentUser(updatedUser);
    localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(updatedUser));
    
    // Update user's last login
    const updatedUsers = users.map(u => u.id === user.id ? updatedUser : u);
    saveUsers(updatedUsers);
    
    return true;
  };

  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem(CURRENT_USER_KEY);
  };

  const hasPermission = (resource: string, action: string, targetDepartment?: string, targetLocation?: string): boolean => {
    if (!currentUser) return false;

    const permission = currentUser.role.permissions.find(p => 
      p.resource === resource && p.action === action
    );

    if (!permission) return false;

    // Check scope restrictions
    switch (permission.scope) {
      case 'all':
        return true;
      case 'department':
        return !targetDepartment || currentUser.department === targetDepartment;
      case 'location':
        return !targetLocation || currentUser.location === targetLocation;
      case 'own':
        return true; // Additional logic needed based on context
      default:
        return false;
    }
  };

  const canAccessAsset = (asset: any): boolean => {
    if (!currentUser) return false;

    // Check if user has read permission for assets
    if (!hasPermission('assets', 'read')) return false;

    // Check department restrictions
    if (currentUser.role.departmentRestrictions.length > 0) {
      if (!currentUser.role.departmentRestrictions.includes(asset.department || currentUser.department)) {
        return false;
      }
    }

    // Check location restrictions
    if (currentUser.role.locationRestrictions.length > 0) {
      if (!currentUser.role.locationRestrictions.includes(asset.location || currentUser.location)) {
        return false;
      }
    }

    return true;
  };

  const addUser = (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User => {
    const password = (userData as any).password || 'password';
    const newUser: User = {
      ...userData,
      id: Date.now().toString(),
      password: hashPassword(password),
      passwordResetRequired: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    const updatedUsers = [...users, newUser];
    saveUsers(updatedUsers);
    return newUser;
  };

  const updateUser = (id: string, updates: Partial<User>) => {
    const updatedUsers = users.map(user => {
      if (user.id === id) {
        const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() };
        // If password is being updated, hash it
        if ((updates as any).password) {
          updatedUser.password = hashPassword((updates as any).password);
        }
        return updatedUser;
      }
      return user;
    });
    saveUsers(updatedUsers);
    
    // Update current user if it's the one being updated
    if (currentUser?.id === id) {
      const updatedCurrentUser = updatedUsers.find(u => u.id === id);
      if (updatedCurrentUser) {
        setCurrentUser(updatedCurrentUser);
        localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(updatedCurrentUser));
      }
    }
  };

  const deleteUser = (id: string) => {
    const updatedUsers = users.filter(user => user.id !== id);
    saveUsers(updatedUsers);
  };

  const resetUserPassword = (userId: string, newPassword: string, requireChange: boolean = true) => {
    const updatedUsers = users.map(user => 
      user.id === userId 
        ? { 
            ...user, 
            password: hashPassword(newPassword),
            passwordResetRequired: requireChange,
            lastPasswordReset: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        : user
    );
    saveUsers(updatedUsers);
    
    // Update current user if it's the one being updated
    if (currentUser?.id === userId) {
      const updatedCurrentUser = updatedUsers.find(u => u.id === userId);
      if (updatedCurrentUser) {
        setCurrentUser(updatedCurrentUser);
        localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(updatedCurrentUser));
      }
    }
  };

  const addRole = (roleData: Omit<UserRole, 'id'>): UserRole => {
    const newRole: UserRole = {
      ...roleData,
      id: Date.now().toString()
    };
    const updatedRoles = [...roles, newRole];
    saveRoles(updatedRoles);
    return newRole;
  };

  const updateRole = (id: string, updates: Partial<UserRole>) => {
    const updatedRoles = roles.map(role => 
      role.id === id ? { ...role, ...updates } : role
    );
    saveRoles(updatedRoles);
  };

  const deleteRole = (id: string) => {
    const updatedRoles = roles.filter(role => role.id !== id && role.isSystemRole !== true);
    saveRoles(updatedRoles);
  };

  const addLocation = (locationData: Omit<Location, 'id' | 'createdAt' | 'updatedAt'>): Location => {
    const newLocation: Location = {
      ...locationData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    const updatedLocations = [...locations, newLocation];
    saveLocations(updatedLocations);
    return newLocation;
  };

  const updateLocation = (id: string, updates: Partial<Location>) => {
    const updatedLocations = locations.map(location => 
      location.id === id 
        ? { ...location, ...updates, updatedAt: new Date().toISOString() }
        : location
    );
    saveLocations(updatedLocations);
  };

  const deleteLocation = (id: string) => {
    const updatedLocations = locations.filter(location => location.id !== id);
    saveLocations(updatedLocations);
  };

  return {
    user: currentUser,
    users,
    roles,
    locations,
    login,
    logout,
    hasPermission,
    canAccessAsset,
    addUser,
    updateUser,
    deleteUser,
    addRole,
    updateRole,
    deleteRole,
    resetUserPassword,
    addLocation,
    updateLocation,
    deleteLocation
  };
};

// Create Auth Context
export const AuthContext = createContext<AuthContext | null>(null);

const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};