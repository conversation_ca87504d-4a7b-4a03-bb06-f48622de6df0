import { useState, useEffect } from 'react';
import { AuditLogEntry, AssetHistoryFilter, AuditTrailSummary } from '../types/AuditTrail';
import { useAuthContext } from '../components/AuthProvider';

const AUDIT_TRAIL_STORAGE_KEY = 'asset-audit-trail';

export const useAuditTrail = () => {
  const [auditLog, setAuditLog] = useState<AuditLogEntry[]>([]);
  const { user } = useAuthContext();

  useEffect(() => {
    const storedAuditLog = localStorage.getItem(AUDIT_TRAIL_STORAGE_KEY);
    if (storedAuditLog) {
      setAuditLog(JSON.parse(storedAuditLog));
    }
  }, []);

  const saveAuditLog = (updatedLog: AuditLogEntry[]) => {
    setAuditLog(updatedLog);
    localStorage.setItem(AUDIT_TRAIL_STORAGE_KEY, JSON.stringify(updatedLog));
  };

  const logAction = (entry: Omit<AuditLogEntry, 'id' | 'timestamp' | 'userId' | 'userName' | 'userRole'>) => {
    if (!user) return;

    const newEntry: AuditLogEntry = {
      ...entry,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      userId: user.id,
      userName: `${user.firstName} ${user.lastName}`,
      userRole: user.role.name,
      ipAddress: 'localhost', // In production, this would be the actual IP
      userAgent: navigator.userAgent
    };

    const updatedLog = [newEntry, ...auditLog];
    saveAuditLog(updatedLog);
  };

  const getAssetHistory = (assetId: string): AuditLogEntry[] => {
    return auditLog.filter(entry => entry.assetId === assetId)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const getFilteredHistory = (filter: AssetHistoryFilter): AuditLogEntry[] => {
    return auditLog.filter(entry => {
      if (filter.assetId && entry.assetId !== filter.assetId) return false;
      if (filter.userId && entry.userId !== filter.userId) return false;
      if (filter.action && entry.action !== filter.action) return false;
      if (filter.field && entry.field !== filter.field) return false;
      
      if (filter.dateFrom) {
        const entryDate = new Date(entry.timestamp);
        const fromDate = new Date(filter.dateFrom);
        if (entryDate < fromDate) return false;
      }
      
      if (filter.dateTo) {
        const entryDate = new Date(entry.timestamp);
        const toDate = new Date(filter.dateTo);
        toDate.setHours(23, 59, 59, 999); // End of day
        if (entryDate > toDate) return false;
      }
      
      return true;
    }).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const getAuditSummary = (): AuditTrailSummary => {
    const totalEntries = auditLog.length;
    const uniqueAssets = new Set(auditLog.map(entry => entry.assetId)).size;
    const uniqueUsers = new Set(auditLog.map(entry => entry.userId)).size;
    
    const actionCounts = auditLog.reduce((acc, entry) => {
      acc[entry.action] = (acc[entry.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const recentActivity = auditLog
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);

    const userCounts = auditLog.reduce((acc, entry) => {
      const key = entry.userId;
      if (!acc[key]) {
        acc[key] = { userId: entry.userId, userName: entry.userName, count: 0 };
      }
      acc[key].count++;
      return acc;
    }, {} as Record<string, { userId: string; userName: string; count: number }>);

    const topUsers = Object.values(userCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalEntries,
      uniqueAssets,
      uniqueUsers,
      actionCounts,
      recentActivity,
      topUsers
    };
  };

  const exportAuditLog = (filter?: AssetHistoryFilter) => {
    const data = filter ? getFilteredHistory(filter) : auditLog;
    const csvHeaders = [
      'Timestamp', 'Asset Name', 'Action', 'Field', 'Old Value', 'New Value',
      'User', 'Role', 'IP Address', 'Notes'
    ];
    
    const csvData = data.map(entry => [
      new Date(entry.timestamp).toLocaleString(),
      entry.assetName,
      entry.action.replace('_', ' ').toUpperCase(),
      entry.field || '',
      entry.oldValue?.toString() || '',
      entry.newValue?.toString() || '',
      entry.userName,
      entry.userRole,
      entry.ipAddress || '',
      entry.notes || ''
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit-trail-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const clearOldEntries = (daysToKeep: number = 365) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const filteredLog = auditLog.filter(entry => 
      new Date(entry.timestamp) >= cutoffDate
    );
    
    saveAuditLog(filteredLog);
    return auditLog.length - filteredLog.length; // Return number of entries removed
  };

  return {
    auditLog,
    logAction,
    getAssetHistory,
    getFilteredHistory,
    getAuditSummary,
    exportAuditLog,
    clearOldEntries
  };
};