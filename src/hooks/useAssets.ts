import { useState, useEffect } from 'react';
import { Asset, CheckInOutRecord } from '../types/Asset';
import { useAuthContext } from '../components/AuthProvider';
import { useAuditTrail } from './useAuditTrail';

const STORAGE_KEY = 'asset-inventory';
const CHECKIN_CHECKOUT_KEY = 'checkin-checkout-records';

const initialAssets: Asset[] = [
  {
    id: '1',
    name: 'MacBook Pro 16"',
    type: 'Hardware',
    category: 'Laptop',
    serialNumber: 'MBP2023001',
    model: 'MacBook Pro 16" M2 Pro',
    manufacturer: 'Apple',
    purchaseDate: '2023-06-15',
    purchasePrice: 249900,
    currentValue: 199900,
    status: 'Active',
    location: 'Animation Studio A',
    assignedTo: '<PERSON>',
    description: 'Primary workstation for 3D animation',
    warrantyExpiry: '2026-06-15',
    lastMaintenance: '2024-01-15',
    nextMaintenance: '2024-07-15',
    vendor: 'Apple Store',
    isRental: false,
    createdAt: '2023-06-15T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: '2',
    name: 'RED Camera Package',
    type: 'Equipment',
    category: 'Camera',
    serialNumber: 'RED2024001',
    model: 'RED Komodo 6K',
    manufacturer: 'RED Digital Cinema',
    purchaseDate: '2024-02-01',
    purchasePrice: 708250,
    currentValue: 649800,
    status: 'Checked Out',
    location: 'Equipment Storage',
    assignedTo: 'Sarah Johnson',
    description: 'Professional cinema camera for live-action shoots',
    warrantyExpiry: '2027-02-01',
    lastMaintenance: '2024-02-01',
    nextMaintenance: '2024-08-01',
    vendor: 'RED Digital Cinema',
    isRental: true,
    rentalDetails: {
      startDate: '2024-03-01',
      endDate: '2024-03-15',
      cost: 99960,
      contactPerson: 'Mike Wilson',
      contactEmail: '<EMAIL>'
    },
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2024-03-01T08:00:00Z'
  },
  {
    id: '3',
    name: 'Maya 2024 License',
    type: 'Software',
    category: '3D Software',
    serialNumber: 'MAYA2024-001',
    model: 'Maya 2024',
    manufacturer: 'Autodesk',
    purchaseDate: '2024-01-01',
    purchasePrice: 148575,
    currentValue: 124875,
    status: 'Active',
    location: 'License Server',
    assignedTo: 'All Animators',
    description: 'Professional 3D animation software license',
    warrantyExpiry: '2025-01-01',
    lastMaintenance: '2024-01-01',
    nextMaintenance: '2025-01-01',
    vendor: 'Autodesk',
    isRental: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

export const useAssets = () => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [checkInOutRecords, setCheckInOutRecords] = useState<CheckInOutRecord[]>([]);
  const { canAccessAsset } = useAuthContext();
  const { logAction } = useAuditTrail();

  useEffect(() => {
    const storedAssets = localStorage.getItem(STORAGE_KEY);
    const storedRecords = localStorage.getItem(CHECKIN_CHECKOUT_KEY);
    
    if (storedAssets) {
      setAssets(JSON.parse(storedAssets));
    } else {
      setAssets(initialAssets);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(initialAssets));
    }

    if (storedRecords) {
      setCheckInOutRecords(JSON.parse(storedRecords));
    }
  }, []);

  // Filter assets based on user permissions
  const filteredAssets = assets.filter(asset => canAccessAsset(asset));

  const saveAssets = (updatedAssets: Asset[]) => {
    setAssets(updatedAssets);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedAssets));
  };

  const saveCheckInOutRecords = (records: CheckInOutRecord[]) => {
    setCheckInOutRecords(records);
    localStorage.setItem(CHECKIN_CHECKOUT_KEY, JSON.stringify(records));
  };

  const addAsset = (asset: Omit<Asset, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAsset: Asset = {
      ...asset,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    const updatedAssets = [...assets, newAsset];
    saveAssets(updatedAssets);

    // Log the creation
    logAction({
      assetId: newAsset.id,
      assetName: newAsset.name,
      action: 'created',
      notes: `Asset created with initial status: ${newAsset.status}`,
      metadata: {
        type: newAsset.type,
        category: newAsset.category,
        isRental: newAsset.isRental
      }
    });

    return newAsset;
  };

  const updateAsset = (id: string, updates: Partial<Asset>) => {
    const oldAsset = assets.find(asset => asset.id === id);
    if (!oldAsset) return;

    const updatedAsset = { ...oldAsset, ...updates, updatedAt: new Date().toISOString() };
    const updatedAssets = assets.map(asset => 
      asset.id === id ? updatedAsset : asset
    );
    saveAssets(updatedAssets);

    // Log each field change
    Object.entries(updates).forEach(([field, newValue]) => {
      if (field === 'updatedAt') return; // Skip timestamp field
      
      const oldValue = (oldAsset as any)[field];
      if (oldValue !== newValue) {
        let action = 'updated';
        let notes = `Updated ${field}`;

        // Special handling for specific fields
        if (field === 'status') {
          action = 'status_changed';
          notes = `Status changed from ${oldValue} to ${newValue}`;
        } else if (field === 'assignedTo') {
          action = newValue ? 'assigned' : 'unassigned';
          notes = newValue ? `Assigned to ${newValue}` : `Unassigned from ${oldValue}`;
        }

        logAction({
          assetId: id,
          assetName: updatedAsset.name,
          action: action as any,
          field,
          oldValue,
          newValue,
          notes,
          metadata: {
            updateType: 'field_change'
          }
        });
      }
    });
  };

  const retireAsset = (id: string) => {
    const asset = assets.find(a => a.id === id);
    if (!asset) return;

    updateAsset(id, { status: 'Retired' });
    
    logAction({
      assetId: id,
      assetName: asset.name,
      action: 'retired',
      notes: 'Asset has been retired and is no longer in active use',
      metadata: {
        previousStatus: asset.status,
        retiredDate: new Date().toISOString()
      }
    });
  };

  const checkInOutAsset = (record: Omit<CheckInOutRecord, 'id'>) => {
    const newRecord: CheckInOutRecord = {
      ...record,
      id: Date.now().toString()
    };
    
    const updatedRecords = [...checkInOutRecords, newRecord];
    saveCheckInOutRecords(updatedRecords);

    // Update asset status
    const status = record.action === 'Check Out' ? 'Checked Out' : 'Active';
    updateAsset(record.assetId, { status });

    // Log the check-in/out action
    logAction({
      assetId: record.assetId,
      assetName: record.assetName,
      action: record.action === 'Check Out' ? 'checked_out' : 'checked_in',
      notes: record.notes || `Asset ${record.action.toLowerCase()} by ${record.person}`,
      metadata: {
        person: record.person,
        vendor: record.vendor,
        dueDate: record.dueDate,
        checkInOutId: newRecord.id
      }
    });
  };

  return {
    assets: filteredAssets, // Return filtered assets based on permissions
    allAssets: assets, // Return all assets for admin functions
    checkInOutRecords,
    addAsset,
    updateAsset,
    retireAsset,
    checkInOutAsset
  };
};