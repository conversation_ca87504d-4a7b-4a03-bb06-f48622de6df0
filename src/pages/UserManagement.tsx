import React, { useState } from 'react';
import { useAuthContext } from '../components/AuthProvider';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Eye, 
  Settings,
  UserCheck,
  Building,
  MapPin,
  Clock,
  Key,
  Lock,
  CheckCircle
} from 'lucide-react';
import { User, UserRole } from '../types/User';

const UserManagement: React.FC = () => {
  const { users, roles, locations, addUser, updateUser, deleteUser, addRole, updateRole, deleteRole, hasPermission, resetUserPassword } = useAuthContext();
  const [activeTab, setActiveTab] = useState<'users' | 'roles'>('users');
  const [showUserForm, setShowUserForm] = useState(false);
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editingRole, setEditingRole] = useState<UserRole | null>(null);
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);

  const [userForm, setUserForm] = useState({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    roleId: '',
    department: '',
    location: '',
    isActive: true,
    password: ''
  });

  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    selectedPermissions: [] as string[],
    departmentRestrictions: [] as string[],
    locationRestrictions: [] as string[]
  });

  const [passwordForm, setPasswordForm] = useState({
    newPassword: '',
    confirmPassword: '',
    requirePasswordChange: true
  });

  const departments = ['IT', 'Animation', 'VFX', 'Production', 'Finance', 'HR', 'Marketing'];
  const availableLocations = locations.filter(loc => loc.isActive).map(loc => loc.name);

  const resetUserForm = () => {
    setUserForm({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      roleId: '',
      department: '',
      location: '',
      isActive: true,
      password: ''
    });
    setEditingUser(null);
  };

  const resetRoleForm = () => {
    setRoleForm({
      name: '',
      description: '',
      selectedPermissions: [],
      departmentRestrictions: [],
      locationRestrictions: []
    });
    setEditingRole(null);
  };

  const resetPasswordForm = () => {
    setPasswordForm({
      newPassword: '',
      confirmPassword: '',
      requirePasswordChange: true
    });
    setResetPasswordUser(null);
  };

  const handleUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const selectedRole = roles.find(r => r.id === userForm.roleId);
    if (!selectedRole) return;

    const userData = {
      username: userForm.username,
      email: userForm.email,
      firstName: userForm.firstName,
      lastName: userForm.lastName,
      role: selectedRole,
      department: userForm.department,
      location: userForm.location,
      isActive: userForm.isActive,
      password: userForm.password || 'password', // Default password if not provided
      passwordResetRequired: false
    };

    if (editingUser) {
      // Don't update password if it's empty during edit
      const updateData = { ...userData };
      if (!userForm.password) {
        delete (updateData as any).password;
      }
      updateUser(editingUser.id, updateData);
    } else {
      addUser(userData);
    }

    resetUserForm();
    setShowUserForm(false);
  };

  const handleRoleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Get all available permissions
    const allPermissions = roles[0]?.permissions || [];
    const selectedPermissions = allPermissions.filter(p => 
      roleForm.selectedPermissions.includes(p.id)
    );

    const roleData = {
      name: roleForm.name,
      description: roleForm.description,
      permissions: selectedPermissions,
      departmentRestrictions: roleForm.departmentRestrictions,
      locationRestrictions: roleForm.locationRestrictions,
      isSystemRole: false
    };

    if (editingRole) {
      updateRole(editingRole.id, roleData);
    } else {
      addRole(roleData);
    }

    resetRoleForm();
    setShowRoleForm(false);
  };

  const handlePasswordReset = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      alert('Password must be at least 6 characters long');
      return;
    }

    if (resetPasswordUser && resetUserPassword) {
      resetUserPassword(resetPasswordUser.id, passwordForm.newPassword, passwordForm.requirePasswordChange);
      alert(`Password reset successfully for ${resetPasswordUser.firstName} ${resetPasswordUser.lastName}`);
      resetPasswordForm();
      setShowPasswordReset(false);
    }
  };

  const handleEditUser = (user: User) => {
    setUserForm({
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roleId: user.role.id,
      department: user.department,
      location: user.location,
      isActive: user.isActive,
      password: '' // Don't pre-fill password for security
    });
    setEditingUser(user);
    setShowUserForm(true);
  };

  const handleEditRole = (role: UserRole) => {
    setRoleForm({
      name: role.name,
      description: role.description,
      selectedPermissions: role.permissions.map(p => p.id),
      departmentRestrictions: role.departmentRestrictions,
      locationRestrictions: role.locationRestrictions
    });
    setEditingRole(role);
    setShowRoleForm(true);
  };

  const handleResetPassword = (user: User) => {
    setResetPasswordUser(user);
    resetPasswordForm();
    setShowPasswordReset(true);
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      deleteUser(userId);
    }
  };

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (role?.isSystemRole) {
      alert('Cannot delete system roles');
      return;
    }
    
    if (window.confirm('Are you sure you want to delete this role?')) {
      deleteRole(roleId);
    }
  };

  const generateRandomPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setPasswordForm(prev => ({ ...prev, newPassword: password, confirmPassword: password }));
  };

  const canManageUsers = hasPermission('users', 'create') || hasPermission('users', 'update');
  const canDeleteUsers = hasPermission('users', 'delete');

  if (!hasPermission('users', 'read')) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to manage users.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600 mt-2">Manage users, roles, and permissions</p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('users')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Users className="w-5 h-5 inline mr-2" />
              Users ({users.length})
            </button>
            <button
              onClick={() => setActiveTab('roles')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'roles'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Shield className="w-5 h-5 inline mr-2" />
              Roles ({roles.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'users' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900">System Users</h2>
                {canManageUsers && (
                  <button
                    onClick={() => {
                      resetUserForm();
                      setShowUserForm(true);
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    Add User
                  </button>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">User</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Role</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Department</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Location</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Last Login</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4">
                          <div>
                            <p className="font-medium text-gray-900">{user.firstName} {user.lastName}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                            <p className="text-xs text-gray-500">@{user.username}</p>
                            {user.passwordResetRequired && (
                              <div className="flex items-center gap-1 mt-1">
                                <Key className="w-3 h-3 text-orange-600" />
                                <span className="text-xs text-orange-600">Password reset required</span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className="inline-block px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                            {user.role.name}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-1">
                            <Building className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{user.department}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-1">
                            <MapPin className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{user.location}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                            user.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          {user.lastLogin ? (
                            <div className="flex items-center gap-1">
                              <Clock className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {new Date(user.lastLogin).toLocaleDateString()}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">Never</span>
                          )}
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex gap-2">
                            {canManageUsers && (
                              <>
                                <button
                                  onClick={() => handleEditUser(user)}
                                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                  title="Edit User"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleResetPassword(user)}
                                  className="p-1 text-gray-400 hover:text-orange-600 transition-colors"
                                  title="Reset Password"
                                >
                                  <Key className="w-4 h-4" />
                                </button>
                              </>
                            )}
                            {canDeleteUsers && user.id !== 'admin-001' && (
                              <button
                                onClick={() => handleDeleteUser(user.id)}
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete User"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900">User Roles</h2>
                {canManageUsers && (
                  <button
                    onClick={() => {
                      resetRoleForm();
                      setShowRoleForm(true);
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    Add Role
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {roles.map((role) => (
                  <div key={role.id} className="bg-gray-50 rounded-lg border border-gray-200 p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900">{role.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{role.description}</p>
                        {role.isSystemRole && (
                          <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mt-2">
                            System Role
                          </span>
                        )}
                      </div>
                      {canManageUsers && !role.isSystemRole && (
                        <div className="flex gap-1">
                          <button
                            onClick={() => handleEditRole(role)}
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteRole(role.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Permissions ({role.permissions.length})</h4>
                        <div className="flex flex-wrap gap-1">
                          {role.permissions.slice(0, 3).map((permission) => (
                            <span key={permission.id} className="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                              {permission.name}
                            </span>
                          ))}
                          {role.permissions.length > 3 && (
                            <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                              +{role.permissions.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>

                      {role.departmentRestrictions.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Department Access</h4>
                          <div className="flex flex-wrap gap-1">
                            {role.departmentRestrictions.map((dept) => (
                              <span key={dept} className="inline-block px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">
                                {dept}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {role.locationRestrictions.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Location Access</h4>
                          <div className="flex flex-wrap gap-1">
                            {role.locationRestrictions.map((location) => (
                              <span key={location} className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                {location}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="pt-2 border-t border-gray-200">
                        <p className="text-xs text-gray-500">
                          {users.filter(u => u.role.id === role.id).length} users assigned
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Form Modal */}
      {showUserForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingUser ? 'Edit User' : 'Add New User'}
            </h3>
            
            <form onSubmit={handleUserSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <input
                    type="text"
                    value={userForm.firstName}
                    onChange={(e) => setUserForm(prev => ({ ...prev, firstName: e.target.value }))}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                  <input
                    type="text"
                    value={userForm.lastName}
                    onChange={(e) => setUserForm(prev => ({ ...prev, lastName: e.target.value }))}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  type="text"
                  value={userForm.username}
                  onChange={(e) => setUserForm(prev => ({ ...prev, username: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={userForm.email}
                  onChange={(e) => setUserForm(prev => ({ ...prev, email: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {editingUser ? 'New Password (leave empty to keep current)' : 'Initial Password'}
                </label>
                <input
                  type="password"
                  value={userForm.password}
                  onChange={(e) => setUserForm(prev => ({ ...prev, password: e.target.value }))}
                  placeholder={editingUser ? "Leave empty to keep current password" : "Leave empty for default password"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {editingUser 
                    ? "Only enter a password if you want to change it" 
                    : "If left empty, default password will be 'password'"
                  }
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select
                  value={userForm.roleId}
                  onChange={(e) => setUserForm(prev => ({ ...prev, roleId: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a role...</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>{role.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select
                  value={userForm.department}
                  onChange={(e) => setUserForm(prev => ({ ...prev, department: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select department...</option>
                  {departments.map((dept) => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <select
                  value={userForm.location}
                  onChange={(e) => setUserForm(prev => ({ ...prev, location: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select location...</option>
                  {availableLocations.map((location) => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={userForm.isActive}
                  onChange={(e) => setUserForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">Active User</label>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingUser ? 'Update User' : 'Create User'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowUserForm(false);
                    resetUserForm();
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Password Reset Modal */}
      {showPasswordReset && resetPasswordUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-orange-100 rounded-full">
                <Key className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Reset Password</h3>
                <p className="text-sm text-gray-600">
                  Reset password for {resetPasswordUser.firstName} {resetPasswordUser.lastName}
                </p>
              </div>
            </div>
            
            <form onSubmit={handlePasswordReset} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                <div className="relative">
                  <input
                    type="password"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                    required
                    minLength={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                  />
                  <button
                    type="button"
                    onClick={generateRandomPassword}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-blue-600"
                    title="Generate Random Password"
                  >
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                <input
                  type="password"
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  required
                  minLength={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={passwordForm.requirePasswordChange}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, requirePasswordChange: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Require password change on next login
                </label>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <Lock className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Security Note:</p>
                    <p>The user will need to use this new password to log in. Consider sharing it securely.</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  Reset Password
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowPasswordReset(false);
                    resetPasswordForm();
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Role Form Modal */}
      {showRoleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingRole ? 'Edit Role' : 'Create New Role'}
            </h3>
            
            <form onSubmit={handleRoleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                <input
                  type="text"
                  value={roleForm.name}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={roleForm.description}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Permissions</label>
                <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-4 space-y-2">
                  {roles[0]?.permissions.map((permission) => (
                    <label key={permission.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={roleForm.selectedPermissions.includes(permission.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setRoleForm(prev => ({
                              ...prev,
                              selectedPermissions: [...prev.selectedPermissions, permission.id]
                            }));
                          } else {
                            setRoleForm(prev => ({
                              ...prev,
                              selectedPermissions: prev.selectedPermissions.filter(id => id !== permission.id)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-900">{permission.name}</span>
                      <span className="ml-2 text-xs text-gray-500">({permission.description})</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department Restrictions</label>
                <p className="text-xs text-gray-500 mb-2">Leave empty for access to all departments</p>
                <div className="grid grid-cols-2 gap-2">
                  {departments.map((dept) => (
                    <label key={dept} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={roleForm.departmentRestrictions.includes(dept)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setRoleForm(prev => ({
                              ...prev,
                              departmentRestrictions: [...prev.departmentRestrictions, dept]
                            }));
                          } else {
                            setRoleForm(prev => ({
                              ...prev,
                              departmentRestrictions: prev.departmentRestrictions.filter(d => d !== dept)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-900">{dept}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Location Restrictions</label>
                <p className="text-xs text-gray-500 mb-2">Leave empty for access to all locations</p>
                <div className="grid grid-cols-2 gap-2">
                  {availableLocations.map((location) => (
                    <label key={location} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={roleForm.locationRestrictions.includes(location)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setRoleForm(prev => ({
                              ...prev,
                              locationRestrictions: [...prev.locationRestrictions, location]
                            }));
                          } else {
                            setRoleForm(prev => ({
                              ...prev,
                              locationRestrictions: prev.locationRestrictions.filter(l => l !== location)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-900">{location}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingRole ? 'Update Role' : 'Create Role'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowRoleForm(false);
                    resetRoleForm();
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;