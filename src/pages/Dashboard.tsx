import React from 'react';
import { useAssets } from '../hooks/useAssets';
import { 
  Package, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  TrendingUp,
  Calendar,
  ShoppingCart
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { assets, checkInOutRecords } = useAssets();

  const stats = {
    totalAssets: assets.length,
    ownedAssets: assets.filter(a => !a.isRental).length,
    rentedAssets: assets.filter(a => a.isRental).length,
    activeAssets: assets.filter(a => a.status === 'Active').length,
    retiredAssets: assets.filter(a => a.status === 'Retired').length,
    totalValue: assets.reduce((sum, asset) => sum + asset.currentValue, 0),
    maintenanceRequired: assets.filter(a => 
      new Date(a.nextMaintenance) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    ).length,
    checkedOut: assets.filter(a => a.status === 'Checked Out').length
  };

  const recentActivity = checkInOutRecords
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  const upcomingMaintenances = assets
    .filter(a => new Date(a.nextMaintenance) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
    .sort((a, b) => new Date(a.nextMaintenance).getTime() - new Date(b.nextMaintenance).getTime())
    .slice(0, 5);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    gradient: string;
    subtitle?: string;
  }> = ({ title, value, icon, gradient, subtitle }) => (
    <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100/50 p-6 hover:shadow-xl transition-all duration-300">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-slate-600">{title}</p>
          <p className="text-2xl font-bold text-slate-900">{value}</p>
          {subtitle && <p className="text-xs text-slate-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-xl ${gradient} shadow-lg`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          Dashboard
        </h1>
        <p className="text-slate-600 mt-2">Overview of your IT assets and recent activity</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Total Assets"
          value={stats.totalAssets}
          icon={<Package className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-blue-500 to-blue-600"
          subtitle={`${stats.ownedAssets} Owned • ${stats.rentedAssets} Rented`}
        />
        <StatCard
          title="Active Assets"
          value={stats.activeAssets}
          icon={<CheckCircle className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-green-500 to-green-600"
        />
        <StatCard
          title="Total Value"
          value={`₹${stats.totalValue.toLocaleString('en-IN')}`}
          icon={<DollarSign className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-purple-500 to-purple-600"
        />
        <StatCard
          title="Checked Out"
          value={stats.checkedOut}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-orange-500 to-orange-600"
        />
        <StatCard
          title="Maintenance Due"
          value={stats.maintenanceRequired}
          icon={<Clock className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-yellow-500 to-yellow-600"
        />
        <StatCard
          title="Retired"
          value={stats.retiredAssets}
          icon={<AlertTriangle className="w-6 h-6 text-white" />}
          gradient="bg-gradient-to-r from-red-500 to-red-600"
        />
      </div>

      {/* Asset Breakdown Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-green-100/50 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg">
              <ShoppingCart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900">Owned Assets</h3>
              <p className="text-sm text-slate-600">Company-purchased assets</p>
            </div>
          </div>
          <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent mb-2">
            {stats.ownedAssets}
          </div>
          <p className="text-sm text-slate-500">
            {((stats.ownedAssets / stats.totalAssets) * 100).toFixed(1)}% of total assets
          </p>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-orange-100/50 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900">Rental Assets</h3>
              <p className="text-sm text-slate-600">Vendor-rented assets</p>
            </div>
          </div>
          <div className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-orange-700 bg-clip-text text-transparent mb-2">
            {stats.rentedAssets}
          </div>
          <p className="text-sm text-slate-500">
            {stats.totalAssets > 0 ? ((stats.rentedAssets / stats.totalAssets) * 100).toFixed(1) : 0}% of total assets
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-purple-100/50">
          <div className="px-6 py-4 border-b border-purple-100/50">
            <h3 className="text-lg font-semibold text-slate-900">Recent Activity</h3>
          </div>
          <div className="p-6">
            {recentActivity.length === 0 ? (
              <p className="text-slate-500 text-center py-4">No recent activity</p>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((record) => (
                  <div key={record.id} className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100/50">
                    <div>
                      <p className="font-medium text-slate-900">{record.assetName}</p>
                      <p className="text-sm text-slate-600">
                        {record.action} by {record.person}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-slate-500">
                        {new Date(record.date).toLocaleDateString()}
                      </p>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                        record.action === 'Check Out' 
                          ? 'bg-orange-100 text-orange-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {record.action}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-yellow-100/50">
          <div className="px-6 py-4 border-b border-yellow-100/50">
            <h3 className="text-lg font-semibold text-slate-900">Upcoming Maintenance</h3>
          </div>
          <div className="p-6">
            {upcomingMaintenances.length === 0 ? (
              <p className="text-slate-500 text-center py-4">No upcoming maintenance</p>
            ) : (
              <div className="space-y-4">
                {upcomingMaintenances.map((asset) => (
                  <div key={asset.id} className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                    <div>
                      <p className="font-medium text-slate-900">{asset.name}</p>
                      <p className="text-sm text-slate-600">{asset.location}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-yellow-800">
                        {new Date(asset.nextMaintenance).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-yellow-600">Due Soon</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;