import React, { useState } from 'react';
import { useAssets } from '../hooks/useAssets';
import { 
  FileText, 
  Download, 
  TrendingUp, 
  Package, 
  DollarSign,
  MapPin,
  Calendar,
  AlertTriangle
} from 'lucide-react';

const Reports: React.FC = () => {
  const { assets, checkInOutRecords } = useAssets();
  const [selectedReport, setSelectedReport] = useState('overview');

  const generateOverviewReport = () => {
    const totalAssets = assets.length;
    const activeAssets = assets.filter(a => a.status === 'Active').length;
    const retiredAssets = assets.filter(a => a.status === 'Retired').length;
    const maintenanceAssets = assets.filter(a => a.status === 'Maintenance').length;
    const checkedOutAssets = assets.filter(a => a.status === 'Checked Out').length;
    const totalValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0);
    const purchaseValue = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0);
    const depreciation = purchaseValue - totalValue;

    const assetsByType = assets.reduce((acc, asset) => {
      acc[asset.type] = (acc[asset.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const assetsByLocation = assets.reduce((acc, asset) => {
      acc[asset.location] = (acc[asset.location] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalAssets,
      activeAssets,
      retiredAssets,
      maintenanceAssets,
      checkedOutAssets,
      totalValue,
      purchaseValue,
      depreciation,
      assetsByType,
      assetsByLocation
    };
  };

  const generateMaintenanceReport = () => {
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    const upcomingMaintenance = assets.filter(asset => {
      const nextMaintenance = new Date(asset.nextMaintenance);
      return nextMaintenance <= thirtyDaysFromNow && nextMaintenance >= now;
    }).sort((a, b) => new Date(a.nextMaintenance).getTime() - new Date(b.nextMaintenance).getTime());

    const overdueMaintenance = assets.filter(asset => {
      const nextMaintenance = new Date(asset.nextMaintenance);
      return nextMaintenance < now;
    });

    return { upcomingMaintenance, overdueMaintenance };
  };

  const generateWarrantyReport = () => {
    const now = new Date();
    const sixMonthsFromNow = new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
    
    const expiringWarranties = assets.filter(asset => {
      const warrantyExpiry = new Date(asset.warrantyExpiry);
      return warrantyExpiry <= sixMonthsFromNow && warrantyExpiry >= now;
    }).sort((a, b) => new Date(a.warrantyExpiry).getTime() - new Date(b.warrantyExpiry).getTime());

    const expiredWarranties = assets.filter(asset => {
      const warrantyExpiry = new Date(asset.warrantyExpiry);
      return warrantyExpiry < now;
    });

    return { expiringWarranties, expiredWarranties };
  };

  const generateActivityReport = () => {
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentActivity = checkInOutRecords.filter(record => 
      new Date(record.date) >= last30Days
    );

    const checkOuts = recentActivity.filter(r => r.action === 'Check Out').length;
    const checkIns = recentActivity.filter(r => r.action === 'Check In').length;

    return {
      totalActivity: recentActivity.length,
      checkOuts,
      checkIns,
      recentActivity: recentActivity.sort((a, b) => 
        new Date(b.date).getTime() - new Date(a.date).getTime()
      ).slice(0, 10)
    };
  };

  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => 
        JSON.stringify(row[header] || '')
      ).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const overviewData = generateOverviewReport();
  const maintenanceData = generateMaintenanceReport();
  const warrantyData = generateWarrantyReport();
  const activityData = generateActivityReport();

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Reports</h1>
        <p className="text-gray-600 mt-2">Generate comprehensive reports on your IT assets</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-wrap gap-4 mb-6">
          <button
            onClick={() => setSelectedReport('overview')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              selectedReport === 'overview'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedReport('maintenance')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              selectedReport === 'maintenance'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Maintenance
          </button>
          <button
            onClick={() => setSelectedReport('warranty')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              selectedReport === 'warranty'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Warranty
          </button>
          <button
            onClick={() => setSelectedReport('activity')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              selectedReport === 'activity'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Activity
          </button>
        </div>

        {selectedReport === 'overview' && (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Asset Overview Report</h2>
              <button
                onClick={() => exportToCSV(assets, 'asset-overview')}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export CSV
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                title="Total Assets"
                value={overviewData.totalAssets}
                icon={<Package className="w-6 h-6 text-white" />}
                color="bg-blue-500"
              />
              <StatCard
                title="Active Assets"
                value={overviewData.activeAssets}
                icon={<TrendingUp className="w-6 h-6 text-white" />}
                color="bg-green-500"
              />
              <StatCard
                title="Total Value"
                value={`₹${overviewData.totalValue.toLocaleString('en-IN')}`}
                icon={<DollarSign className="w-6 h-6 text-white" />}
                color="bg-purple-500"
              />
              <StatCard
                title="Depreciation"
                value={`₹${overviewData.depreciation.toLocaleString('en-IN')}`}
                icon={<TrendingUp className="w-6 h-6 text-white" />}
                color="bg-red-500"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Assets by Type</h3>
                <div className="space-y-3">
                  {Object.entries(overviewData.assetsByType).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-900">{type}</span>
                      <span className="text-gray-600">{count} assets</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Assets by Location</h3>
                <div className="space-y-3">
                  {Object.entries(overviewData.assetsByLocation).map(([location, count]) => (
                    <div key={location} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-900">{location}</span>
                      <span className="text-gray-600">{count} assets</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'maintenance' && (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Maintenance Report</h2>
              <button
                onClick={() => exportToCSV([...maintenanceData.upcomingMaintenance, ...maintenanceData.overdueMaintenance], 'maintenance-report')}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export CSV
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-yellow-600" />
                  Upcoming Maintenance (30 days)
                </h3>
                {maintenanceData.upcomingMaintenance.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No upcoming maintenance scheduled</p>
                ) : (
                  <div className="space-y-3">
                    {maintenanceData.upcomingMaintenance.map((asset) => (
                      <div key={asset.id} className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h4 className="font-medium text-gray-900">{asset.name}</h4>
                        <p className="text-sm text-gray-600">{asset.location}</p>
                        <p className="text-sm font-medium text-yellow-800">
                          Due: {new Date(asset.nextMaintenance).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  Overdue Maintenance
                </h3>
                {maintenanceData.overdueMaintenance.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No overdue maintenance</p>
                ) : (
                  <div className="space-y-3">
                    {maintenanceData.overdueMaintenance.map((asset) => (
                      <div key={asset.id} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h4 className="font-medium text-gray-900">{asset.name}</h4>
                        <p className="text-sm text-gray-600">{asset.location}</p>
                        <p className="text-sm font-medium text-red-800">
                          Overdue since: {new Date(asset.nextMaintenance).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'warranty' && (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Warranty Report</h2>
              <button
                onClick={() => exportToCSV([...warrantyData.expiringWarranties, ...warrantyData.expiredWarranties], 'warranty-report')}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export CSV
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-orange-600" />
                  Expiring Warranties (6 months)
                </h3>
                {warrantyData.expiringWarranties.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No warranties expiring soon</p>
                ) : (
                  <div className="space-y-3">
                    {warrantyData.expiringWarranties.map((asset) => (
                      <div key={asset.id} className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                        <h4 className="font-medium text-gray-900">{asset.name}</h4>
                        <p className="text-sm text-gray-600">{asset.model}</p>
                        <p className="text-sm font-medium text-orange-800">
                          Expires: {new Date(asset.warrantyExpiry).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  Expired Warranties
                </h3>
                {warrantyData.expiredWarranties.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No expired warranties</p>
                ) : (
                  <div className="space-y-3">
                    {warrantyData.expiredWarranties.map((asset) => (
                      <div key={asset.id} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h4 className="font-medium text-gray-900">{asset.name}</h4>
                        <p className="text-sm text-gray-600">{asset.model}</p>
                        <p className="text-sm font-medium text-red-800">
                          Expired: {new Date(asset.warrantyExpiry).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'activity' && (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Activity Report (Last 30 Days)</h2>
              <button
                onClick={() => exportToCSV(activityData.recentActivity, 'activity-report')}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export CSV
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <StatCard
                title="Total Activity"
                value={activityData.totalActivity}
                icon={<FileText className="w-6 h-6 text-white" />}
                color="bg-blue-500"
              />
              <StatCard
                title="Check Outs"
                value={activityData.checkOuts}
                icon={<TrendingUp className="w-6 h-6 text-white" />}
                color="bg-orange-500"
              />
              <StatCard
                title="Check Ins"
                value={activityData.checkIns}
                icon={<TrendingUp className="w-6 h-6 text-white" />}
                color="bg-green-500"
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              {activityData.recentActivity.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No recent activity</p>
              ) : (
                <div className="space-y-3">
                  {activityData.recentActivity.map((record) => (
                    <div key={record.id} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{record.assetName}</h4>
                          <p className="text-sm text-gray-600">
                            {record.action} by {record.person}
                          </p>
                          {record.vendor && (
                            <p className="text-sm text-gray-600">Vendor: {record.vendor}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">
                            {new Date(record.date).toLocaleDateString()}
                          </p>
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                            record.action === 'Check Out' 
                              ? 'bg-orange-100 text-orange-800' 
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {record.action}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Reports;