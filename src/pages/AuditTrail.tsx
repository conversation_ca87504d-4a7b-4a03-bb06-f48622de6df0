import React, { useState, useMemo } from 'react';
import { useAuditTrail } from '../hooks/useAuditTrail';
import { useAssets } from '../hooks/useAssets';
import { useAuthContext } from '../components/AuthProvider';
import { 
  Shield, 
  Download, 
  Filter, 
  Search, 
  Clock, 
  User, 
  Activity,
  TrendingUp,
  FileText,
  AlertTriangle,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AssetHistoryFilter } from '../types/AuditTrail';

const AuditTrail: React.FC = () => {
  const { getFilteredHistory, getAuditSummary, exportAuditLog, clearOldEntries } = useAuditTrail();
  const { assets } = useAssets();
  const { users } = useAuthContext();
  
  const [filter, setFilter] = useState<AssetHistoryFilter>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<string | null>(null);

  const summary = getAuditSummary();
  const filteredHistory = getFilteredHistory(filter);

  // Filter by search term
  const searchFilteredHistory = useMemo(() => {
    if (!searchTerm) return filteredHistory;
    
    return filteredHistory.filter(entry =>
      entry.assetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.notes?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [filteredHistory, searchTerm]);

  const handleExport = () => {
    exportAuditLog(filter);
  };

  const handleClearOldEntries = () => {
    const daysToKeep = parseInt(prompt('How many days of history would you like to keep? (default: 365)') || '365');
    if (isNaN(daysToKeep) || daysToKeep < 1) return;
    
    const removedCount = clearOldEntries(daysToKeep);
    alert(`Removed ${removedCount} old audit entries. Kept entries from the last ${daysToKeep} days.`);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created': return <div className="w-3 h-3 bg-green-500 rounded-full" />;
      case 'updated': return <div className="w-3 h-3 bg-blue-500 rounded-full" />;
      case 'retired': return <div className="w-3 h-3 bg-red-500 rounded-full" />;
      case 'checked_in': return <div className="w-3 h-3 bg-green-500 rounded-full" />;
      case 'checked_out': return <div className="w-3 h-3 bg-orange-500 rounded-full" />;
      case 'status_changed': return <div className="w-3 h-3 bg-purple-500 rounded-full" />;
      case 'assigned': return <div className="w-3 h-3 bg-blue-500 rounded-full" />;
      case 'unassigned': return <div className="w-3 h-3 bg-gray-500 rounded-full" />;
      default: return <div className="w-3 h-3 bg-gray-400 rounded-full" />;
    }
  };

  const formatActionText = (entry: any) => {
    switch (entry.action) {
      case 'created': return 'Asset created';
      case 'updated': return entry.field ? `Updated ${entry.field}` : 'Asset updated';
      case 'retired': return 'Asset retired';
      case 'checked_in': return 'Checked in';
      case 'checked_out': return 'Checked out';
      case 'status_changed': return 'Status changed';
      case 'assigned': return 'Asset assigned';
      case 'unassigned': return 'Asset unassigned';
      default: return entry.action.replace('_', ' ');
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <Shield className="w-8 h-8 text-blue-600" />
          Audit Trail
        </h1>
        <p className="text-gray-600 mt-2">Complete audit trail of all asset changes and activities</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Entries"
          value={summary.totalEntries.toLocaleString()}
          icon={<FileText className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Assets Tracked"
          value={summary.uniqueAssets}
          icon={<Activity className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title="Active Users"
          value={summary.uniqueUsers}
          icon={<User className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
        <StatCard
          title="Recent Activity"
          value={summary.recentActivity.length}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-orange-500"
        />
      </div>

      {/* Action Summary */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Breakdown</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(summary.actionCounts).map(([action, count]) => (
            <div key={action} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              {getActionIcon(action)}
              <div>
                <p className="text-sm font-medium text-gray-900 capitalize">
                  {action.replace('_', ' ')}
                </p>
                <p className="text-lg font-bold text-gray-700">{count}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search audit entries..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              Filters
              {showFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
            <button
              onClick={handleClearOldEntries}
              className="flex items-center gap-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
            >
              <AlertTriangle className="w-4 h-4" />
              Cleanup
            </button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="border-t border-gray-200 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Asset</label>
                <select
                  value={filter.assetId || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, assetId: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Assets</option>
                  {assets.map((asset) => (
                    <option key={asset.id} value={asset.id}>{asset.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">User</label>
                <select
                  value={filter.userId || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, userId: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Users</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.firstName} {user.lastName}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Action</label>
                <select
                  value={filter.action || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, action: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Actions</option>
                  <option value="created">Created</option>
                  <option value="updated">Updated</option>
                  <option value="retired">Retired</option>
                  <option value="checked_in">Checked In</option>
                  <option value="checked_out">Checked Out</option>
                  <option value="status_changed">Status Changed</option>
                  <option value="assigned">Assigned</option>
                  <option value="unassigned">Unassigned</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <div className="flex gap-2">
                  <input
                    type="date"
                    value={filter.dateFrom || ''}
                    onChange={(e) => setFilter(prev => ({ ...prev, dateFrom: e.target.value || undefined }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="date"
                    value={filter.dateTo || ''}
                    onChange={(e) => setFilter(prev => ({ ...prev, dateTo: e.target.value || undefined }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={() => {
                  setFilter({});
                  setSearchTerm('');
                }}
                className="px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Audit Entries */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Audit Entries ({searchFilteredHistory.length})
          </h3>
        </div>

        <div className="divide-y divide-gray-200">
          {searchFilteredHistory.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Audit Entries</h3>
              <p className="text-gray-600">No audit entries match your current filters.</p>
            </div>
          ) : (
            searchFilteredHistory.map((entry) => (
              <div key={entry.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 flex-shrink-0">
                      {getActionIcon(entry.action)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">{entry.assetName}</h4>
                        <span className="text-sm text-gray-500">•</span>
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {formatActionText(entry)}
                        </span>
                      </div>
                      
                      {entry.field && (entry.oldValue !== undefined || entry.newValue !== undefined) && (
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">{entry.field}:</span>
                          {entry.oldValue !== undefined && (
                            <span className="ml-1">
                              "<span className="line-through text-red-600">{entry.oldValue}</span>"
                            </span>
                          )}
                          {entry.oldValue !== undefined && entry.newValue !== undefined && (
                            <span className="mx-1">→</span>
                          )}
                          {entry.newValue !== undefined && (
                            <span className="text-green-600">"{entry.newValue}"</span>
                          )}
                        </div>
                      )}
                      
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          <span>{entry.userName}</span>
                          <span className="text-gray-400">({entry.userRole})</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{new Date(entry.timestamp).toLocaleString()}</span>
                        </div>
                      </div>
                      
                      {entry.notes && (
                        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                          <span className="font-medium text-blue-800">Notes:</span>
                          <span className="ml-1 text-blue-700">{entry.notes}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => setSelectedEntry(selectedEntry === entry.id ? null : entry.id)}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
                
                {selectedEntry === entry.id && entry.metadata && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Technical Details:</h5>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">IP Address:</span>
                        <span className="ml-1 text-gray-700">{entry.ipAddress}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">User Agent:</span>
                        <span className="ml-1 text-gray-700 truncate">{entry.userAgent}</span>
                      </div>
                      {Object.entries(entry.metadata).map(([key, value]) => (
                        <div key={key}>
                          <span className="font-medium text-gray-600">{key}:</span>
                          <span className="ml-1 text-gray-700">{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AuditTrail;