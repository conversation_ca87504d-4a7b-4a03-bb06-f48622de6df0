import React, { useState } from 'react';
import { useAuthContext } from '../components/AuthProvider';
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Building, 
  Users,
  CheckCircle,
  XCircle,
  Save,
  X
} from 'lucide-react';
import { Location } from '../hooks/useAuth';

const LocationManagement: React.FC = () => {
  const { 
    locations, 
    users, 
    addLocation, 
    updateLocation, 
    deleteLocation, 
    hasPermission 
  } = useAuthContext();
  
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  
  const [locationForm, setLocationForm] = useState({
    name: '',
    description: '',
    address: '',
    isActive: true
  });

  const resetLocationForm = () => {
    setLocationForm({
      name: '',
      description: '',
      address: '',
      isActive: true
    });
    setEditingLocation(null);
  };

  const handleLocationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const locationData = {
      name: locationForm.name,
      description: locationForm.description,
      address: locationForm.address,
      isActive: locationForm.isActive
    };

    if (editingLocation) {
      updateLocation(editingLocation.id, locationData);
    } else {
      addLocation(locationData);
    }

    resetLocationForm();
    setShowLocationForm(false);
  };

  const handleEditLocation = (location: Location) => {
    setLocationForm({
      name: location.name,
      description: location.description,
      address: location.address,
      isActive: location.isActive
    });
    setEditingLocation(location);
    setShowLocationForm(true);
  };

  const handleDeleteLocation = (locationId: string) => {
    const location = locations.find(l => l.id === locationId);
    if (!location) return;

    // Check if any users are assigned to this location
    const usersAtLocation = users.filter(u => u.location === location.name);
    
    if (usersAtLocation.length > 0) {
      alert(`Cannot delete location "${location.name}" because ${usersAtLocation.length} user(s) are assigned to it. Please reassign users first.`);
      return;
    }

    if (window.confirm(`Are you sure you want to delete location "${location.name}"?`)) {
      deleteLocation(locationId);
    }
  };

  const canManageLocations = hasPermission('locations', 'create') || hasPermission('locations', 'update');
  const canDeleteLocations = hasPermission('locations', 'delete');

  if (!hasPermission('locations', 'read')) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to manage locations.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Location Management</h1>
        <p className="text-gray-600 mt-2">Manage office locations and facilities</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Company Locations</h2>
            <p className="text-sm text-gray-600">Manage all office locations and facilities</p>
          </div>
          {canManageLocations && (
            <button
              onClick={() => {
                resetLocationForm();
                setShowLocationForm(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Location
            </button>
          )}
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {locations.map((location) => {
              const usersAtLocation = users.filter(u => u.location === location.name);
              
              return (
                <div key={location.id} className="bg-gray-50 rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-full ${location.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <MapPin className={`w-5 h-5 ${location.isActive ? 'text-green-600' : 'text-gray-400'}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{location.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{location.description}</p>
                      </div>
                    </div>
                    
                    {canManageLocations && (
                      <div className="flex gap-1">
                        <button
                          onClick={() => handleEditLocation(location)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit Location"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        {canDeleteLocations && (
                          <button
                            onClick={() => handleDeleteLocation(location.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete Location"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Building className="w-4 h-4 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-600">{location.address}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {usersAtLocation.length} user{usersAtLocation.length !== 1 ? 's' : ''} assigned
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      {location.isActive ? (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-700 font-medium">Active</span>
                        </>
                      ) : (
                        <>
                          <XCircle className="w-4 h-4 text-red-600" />
                          <span className="text-sm text-red-700 font-medium">Inactive</span>
                        </>
                      )}
                    </div>

                    {usersAtLocation.length > 0 && (
                      <div className="pt-2 border-t border-gray-200">
                        <p className="text-xs text-gray-500 mb-2">Users at this location:</p>
                        <div className="flex flex-wrap gap-1">
                          {usersAtLocation.slice(0, 3).map((user) => (
                            <span key={user.id} className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                              {user.firstName} {user.lastName}
                            </span>
                          ))}
                          {usersAtLocation.length > 3 && (
                            <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                              +{usersAtLocation.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="pt-2 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        Created: {new Date(location.createdAt).toLocaleDateString()}
                      </p>
                      {location.updatedAt !== location.createdAt && (
                        <p className="text-xs text-gray-500">
                          Updated: {new Date(location.updatedAt).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {locations.length === 0 && (
            <div className="text-center py-12">
              <MapPin className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Locations</h3>
              <p className="text-gray-600 mb-4">Get started by adding your first location.</p>
              {canManageLocations && (
                <button
                  onClick={() => {
                    resetLocationForm();
                    setShowLocationForm(true);
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                >
                  <Plus className="w-4 h-4" />
                  Add Location
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Location Form Modal */}
      {showLocationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-blue-100 rounded-full">
                <MapPin className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {editingLocation ? 'Edit Location' : 'Add New Location'}
              </h3>
            </div>
            
            <form onSubmit={handleLocationSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location Name *</label>
                <input
                  type="text"
                  value={locationForm.name}
                  onChange={(e) => setLocationForm(prev => ({ ...prev, name: e.target.value }))}
                  required
                  placeholder="e.g., Head Office, Studio A"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={locationForm.description}
                  onChange={(e) => setLocationForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  placeholder="Brief description of this location"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <textarea
                  value={locationForm.address}
                  onChange={(e) => setLocationForm(prev => ({ ...prev, address: e.target.value }))}
                  rows={2}
                  placeholder="Full address of this location"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={locationForm.isActive}
                  onChange={(e) => setLocationForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">Active Location</label>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Location Usage:</p>
                    <p>This location will be available for user assignments and asset tracking.</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  {editingLocation ? 'Update Location' : 'Create Location'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowLocationForm(false);
                    resetLocationForm();
                  }}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationManagement;