import React, { useState } from 'react';
import { useAssets } from '../hooks/useAssets';
import { Bo<PERSON>, Send, Sparkles } from 'lucide-react';

interface AIResponse {
  query: string;
  response: string;
  timestamp: string;
}

const AIQuery: React.FC = () => {
  const { assets, checkInOutRecords } = useAssets();
  const [query, setQuery] = useState('');
  const [responses, setResponses] = useState<AIResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const generateAIResponse = (query: string): string => {
    const lowerQuery = query.toLowerCase();
    
    // Asset counts and statistics
    if (lowerQuery.includes('how many') || lowerQuery.includes('count')) {
      if (lowerQuery.includes('total') || lowerQuery.includes('all')) {
        return `You currently have ${assets.length} total assets in your inventory.`;
      }
      if (lowerQuery.includes('active')) {
        const activeCount = assets.filter(a => a.status === 'Active').length;
        return `You have ${activeCount} active assets currently in use.`;
      }
      if (lowerQuery.includes('retired')) {
        const retiredCount = assets.filter(a => a.status === 'Retired').length;
        return `You have ${retiredCount} retired assets.`;
      }
      if (lowerQuery.includes('hardware')) {
        const hardwareCount = assets.filter(a => a.type === 'Hardware').length;
        return `You have ${hardwareCount} hardware assets.`;
      }
      if (lowerQuery.includes('software')) {
        const softwareCount = assets.filter(a => a.type === 'Software').length;
        return `You have ${softwareCount} software assets.`;
      }
    }

    // Value and financial queries
    if (lowerQuery.includes('value') || lowerQuery.includes('worth') || lowerQuery.includes('cost')) {
      const totalValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0);
      const purchaseValue = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0);
      return `Your total asset portfolio is worth ₹${totalValue.toLocaleString('en-IN')} (current value). You originally purchased these assets for ₹${purchaseValue.toLocaleString('en-IN')}.`;
    }

    // Status queries
    if (lowerQuery.includes('maintenance')) {
      const maintenanceAssets = assets.filter(a => a.status === 'Maintenance');
      if (maintenanceAssets.length === 0) {
        return "Great news! You currently have no assets in maintenance.";
      }
      return `You have ${maintenanceAssets.length} assets currently in maintenance: ${maintenanceAssets.map(a => a.name).join(', ')}.`;
    }

    if (lowerQuery.includes('checked out') || lowerQuery.includes('rental')) {
      const checkedOutAssets = assets.filter(a => a.status === 'Checked Out');
      if (checkedOutAssets.length === 0) {
        return "No assets are currently checked out to vendors.";
      }
      return `You have ${checkedOutAssets.length} assets checked out: ${checkedOutAssets.map(a => a.name).join(', ')}.`;
    }

    // Location queries
    if (lowerQuery.includes('location') || lowerQuery.includes('where')) {
      const locations = [...new Set(assets.map(a => a.location))];
      return `Your assets are located in ${locations.length} different locations: ${locations.join(', ')}.`;
    }

    // Specific asset queries
    if (lowerQuery.includes('macbook') || lowerQuery.includes('laptop')) {
      const laptops = assets.filter(a => 
        a.name.toLowerCase().includes('macbook') || 
        a.category.toLowerCase().includes('laptop')
      );
      if (laptops.length === 0) {
        return "I don't see any MacBooks or laptops in your inventory.";
      }
      return `I found ${laptops.length} laptop(s): ${laptops.map(a => `${a.name} (${a.status})`).join(', ')}.`;
    }

    if (lowerQuery.includes('camera') || lowerQuery.includes('red')) {
      const cameras = assets.filter(a => 
        a.name.toLowerCase().includes('camera') || 
        a.name.toLowerCase().includes('red') ||
        a.category.toLowerCase().includes('camera')
      );
      if (cameras.length === 0) {
        return "I don't see any cameras in your inventory.";
      }
      return `I found ${cameras.length} camera(s): ${cameras.map(a => `${a.name} (${a.status})`).join(', ')}.`;
    }

    // Warranty queries
    if (lowerQuery.includes('warranty') || lowerQuery.includes('expire')) {
      const now = new Date();
      const sixMonthsFromNow = new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
      const expiringWarranties = assets.filter(asset => {
        const warrantyExpiry = new Date(asset.warrantyExpiry);
        return warrantyExpiry <= sixMonthsFromNow && warrantyExpiry >= now;
      });
      
      if (expiringWarranties.length === 0) {
        return "No warranties are expiring in the next 6 months.";
      }
      return `You have ${expiringWarranties.length} warranties expiring in the next 6 months: ${expiringWarranties.map(a => `${a.name} (${new Date(a.warrantyExpiry).toLocaleDateString()})`).join(', ')}.`;
    }

    // Recent activity
    if (lowerQuery.includes('recent') || lowerQuery.includes('activity') || lowerQuery.includes('latest')) {
      const recentRecords = checkInOutRecords
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 3);
      
      if (recentRecords.length === 0) {
        return "No recent check-in/out activity recorded.";
      }
      
      const activities = recentRecords.map(record => 
        `${record.assetName} was ${record.action.toLowerCase()} by ${record.person} on ${new Date(record.date).toLocaleDateString()}`
      );
      
      return `Here's your recent activity: ${activities.join('; ')}.`;
    }

    // Default responses for common queries
    if (lowerQuery.includes('hello') || lowerQuery.includes('hi')) {
      return "Hello! I'm your AI assistant for asset management. I can help you find information about your assets, check statuses, and provide insights about your inventory. What would you like to know?";
    }

    if (lowerQuery.includes('help')) {
      return "I can help you with questions about your assets! Try asking things like: 'How many assets do I have?', 'What's the total value?', 'Which assets need maintenance?', 'What's checked out?', or 'Show me recent activity'.";
    }

    // Fallback response
    return `I understand you're asking about "${query}". While I can see you have ${assets.length} assets in your inventory, I might need more specific information to give you the exact answer you're looking for. Try asking about asset counts, values, maintenance schedules, or recent activity.`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const response = generateAIResponse(query);
    
    const newResponse: AIResponse = {
      query,
      response,
      timestamp: new Date().toISOString()
    };

    setResponses(prev => [newResponse, ...prev]);
    setQuery('');
    setIsLoading(false);
  };

  const suggestedQueries = [
    "How many assets do I have in total?",
    "What's the total value of my assets?",
    "Which assets need maintenance?",
    "What's currently checked out?",
    "Show me recent activity",
    "Where are my cameras located?",
    "Which warranties are expiring soon?"
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <Bot className="w-8 h-8 text-blue-600" />
          AI Asset Query
        </h1>
        <p className="text-gray-600 mt-2">Ask questions about your assets and get intelligent insights</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form onSubmit={handleSubmit} className="mb-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Ask me anything about your assets..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12"
                disabled={isLoading}
              />
              <Sparkles className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
            <button
              type="submit"
              disabled={isLoading || !query.trim()}
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-4 h-4" />
              {isLoading ? 'Thinking...' : 'Ask'}
            </button>
          </div>
        </form>

        <div className="mb-8">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Suggested Queries:</h3>
          <div className="flex flex-wrap gap-2">
            {suggestedQueries.map((suggested, index) => (
              <button
                key={index}
                onClick={() => setQuery(suggested)}
                className="px-3 py-1 text-xs bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100 transition-colors"
                disabled={isLoading}
              >
                {suggested}
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-6">
          {responses.length === 0 ? (
            <div className="text-center py-12">
              <Bot className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Ask me a question to get started!</p>
            </div>
          ) : (
            responses.map((response, index) => (
              <div key={index} className="space-y-4">
                <div className="flex justify-end">
                  <div className="max-w-3xl bg-blue-600 text-white rounded-lg p-4">
                    <p className="text-sm">{response.query}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Bot className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1 bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-900 whitespace-pre-wrap">{response.response}</p>
                    <p className="text-xs text-gray-500 mt-2">
                      {new Date(response.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Sparkles className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-800">AI Assistant Features</h4>
            <p className="text-sm text-blue-700 mt-1">
              I can help you with asset counts, financial summaries, maintenance schedules, location queries, 
              warranty information, and activity reports. Just ask in natural language!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIQuery;