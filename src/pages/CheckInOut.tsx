import React, { useState } from 'react';
import { useAssets } from '../hooks/useAssets';
import { ArrowRightLeft, CheckCircle, Clock, Calendar, AlertTriangle } from 'lucide-react';

const CheckInOut: React.FC = () => {
  const { assets, checkInOutRecords, checkInOutAsset } = useAssets();
  const [formData, setFormData] = useState({
    assetId: '',
    action: 'Check Out' as 'Check In' | 'Check Out',
    person: '',
    vendor: '',
    notes: '',
    dueDate: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const asset = assets.find(a => a.id === formData.assetId);
    if (!asset) return;

    checkInOutAsset({
      assetId: formData.assetId,
      assetName: asset.name,
      action: formData.action,
      date: new Date().toISOString(),
      person: formData.person,
      vendor: formData.vendor,
      notes: formData.notes,
      dueDate: formData.dueDate || undefined
    });

    setFormData({
      assetId: '',
      action: 'Check Out',
      person: '',
      vendor: '',
      notes: '',
      dueDate: ''
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Only show rental assets for check-in/out operations
  const rentalAssets = assets.filter(asset => 
    asset.isRental && 
    asset.status !== 'Retired' && 
    (formData.action === 'Check Out' ? asset.status !== 'Checked Out' : asset.status === 'Checked Out')
  );

  const recentRecords = checkInOutRecords
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 10);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Check In / Check Out</h1>
        <p className="text-gray-600 mt-2">Manage rental asset check-ins and check-outs</p>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <ArrowRightLeft className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-800">Rental Assets Only</h4>
            <p className="text-sm text-blue-700 mt-1">
              This screen is for managing rental assets only. Purchased assets cannot be checked in/out as they are owned by the company.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
            <ArrowRightLeft className="w-5 h-5" />
            Rental Asset Check In/Out
          </h2>

          {rentalAssets.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No rental assets available</p>
              <p className="text-sm text-gray-400">
                {formData.action === 'Check Out' 
                  ? 'All rental assets are currently checked out or no rental assets exist.'
                  : 'No rental assets are currently checked out.'}
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Action *</label>
                <select
                  name="action"
                  value={formData.action}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Check Out">Check Out</option>
                  <option value="Check In">Check In</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Rental Asset *</label>
                <select
                  name="assetId"
                  value={formData.assetId}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">
                    Select a rental asset to {formData.action.toLowerCase()}...
                  </option>
                  {rentalAssets.map((asset) => (
                    <option key={asset.id} value={asset.id}>
                      {asset.name} - {asset.serialNumber} ({asset.status})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.action === 'Check Out' ? 'Assigned To' : 'Returned By'} *
                </label>
                <input
                  type="text"
                  name="person"
                  value={formData.person}
                  onChange={handleChange}
                  required
                  placeholder="Person's name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Rental Vendor</label>
                <input
                  type="text"
                  name="vendor"
                  value={formData.vendor}
                  onChange={handleChange}
                  placeholder="Rental vendor name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {formData.action === 'Check Out' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                  <input
                    type="date"
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Additional notes or comments"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <button
                type="submit"
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <CheckCircle className="w-4 h-4" />
                {formData.action} Rental Asset
              </button>
            </form>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Recent Activity
          </h2>

          <div className="space-y-4">
            {recentRecords.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No check-in/out records yet</p>
            ) : (
              recentRecords.map((record) => (
                <div
                  key={record.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">{record.assetName}</h4>
                      <p className="text-sm text-gray-600">
                        {record.action} by {record.person}
                      </p>
                    </div>
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      record.action === 'Check Out' 
                        ? 'bg-orange-100 text-orange-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {record.action}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(record.date).toLocaleDateString()} at{' '}
                    {new Date(record.date).toLocaleTimeString()}
                  </div>
                  
                  {record.vendor && (
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-medium">Vendor:</span> {record.vendor}
                    </p>
                  )}
                  
                  {record.dueDate && (
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-medium">Due:</span> {new Date(record.dueDate).toLocaleDateString()}
                    </p>
                  )}
                  
                  {record.notes && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Notes:</span> {record.notes}
                    </p>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckInOut;