import React, { useState, useRef } from 'react';
import { useAssets } from '../hooks/useAssets';
import { useNavigate } from 'react-router-dom';
import { 
  Upload, 
  Download, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  X,
  FileSpreadsheet,
  Database
} from 'lucide-react';
import { Asset } from '../types/Asset';

const BulkImportExport: React.FC = () => {
  const { assets, addAsset } = useAssets();
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [importResults, setImportResults] = useState<{
    success: number;
    errors: string[];
    total: number;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const csvTemplate = `name,type,category,serialNumber,model,manufacturer,purchaseDate,purchasePrice,currentValue,status,location,assignedTo,description,warrantyExpiry,lastMaintenance,nextMaintenance,vendor,isRental,rentalStartDate,rentalEndDate,rentalCost,rentalContactPerson,rentalContactEmail
MacBook Pro 16",Hardware,Laptop,MBP2024001,MacBook Pro 16" M3,Apple,2024-01-15,250000,230000,Active,Animation Studio A,John Doe,Primary workstation for 3D animation,2027-01-15,2024-01-15,2024-07-15,Apple Store,false,,,,,
RED Camera Kit,Equipment,Camera,RED2024002,RED Komodo 6K,RED Digital Cinema,2024-02-01,700000,650000,Checked Out,Equipment Storage,Sarah Johnson,Professional cinema camera,2027-02-01,2024-02-01,2024-08-01,RED Rentals,true,2024-03-01,2024-03-15,100000,Mike Wilson,<EMAIL>`;

  const parseCSV = (csvText: string): any[] => {
    const lines = csvText.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = [];
      let current = '';
      let inQuotes = false;
      
      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      if (values.length === headers.length) {
        const row: any = {};
        headers.forEach((header, index) => {
          row[header] = values[index];
        });
        data.push(row);
      }
    }

    return data;
  };

  const validateAssetData = (data: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Asset name is required');
    }

    if (!data.type || !['Hardware', 'Software', 'Equipment', 'Furniture', 'Vehicle'].includes(data.type)) {
      errors.push('Valid asset type is required (Hardware, Software, Equipment, Furniture, Vehicle)');
    }

    if (!data.status || !['Active', 'Inactive', 'Maintenance', 'Retired', 'Checked Out'].includes(data.status)) {
      errors.push('Valid status is required (Active, Inactive, Maintenance, Retired, Checked Out)');
    }

    if (data.purchasePrice && isNaN(parseFloat(data.purchasePrice))) {
      errors.push('Purchase price must be a valid number');
    }

    if (data.currentValue && isNaN(parseFloat(data.currentValue))) {
      errors.push('Current value must be a valid number');
    }

    if (data.isRental && data.isRental.toLowerCase() === 'true') {
      if (data.rentalCost && isNaN(parseFloat(data.rentalCost))) {
        errors.push('Rental cost must be a valid number');
      }
    }

    return { isValid: errors.length === 0, errors };
  };

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setImportResults(null);

    try {
      const text = await file.text();
      const data = parseCSV(text);
      
      let successCount = 0;
      const errors: string[] = [];

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const validation = validateAssetData(row);
        
        if (validation.isValid) {
          try {
            const assetData = {
              name: row.name,
              type: row.type as Asset['type'],
              category: row.category || '',
              serialNumber: row.serialNumber || '',
              model: row.model || '',
              manufacturer: row.manufacturer || '',
              purchaseDate: row.purchaseDate || '',
              purchasePrice: parseFloat(row.purchasePrice) || 0,
              currentValue: parseFloat(row.currentValue) || 0,
              status: row.status as Asset['status'],
              location: row.location || '',
              assignedTo: row.assignedTo || '',
              description: row.description || '',
              warrantyExpiry: row.warrantyExpiry || '',
              lastMaintenance: row.lastMaintenance || '',
              nextMaintenance: row.nextMaintenance || '',
              vendor: row.vendor || '',
              isRental: row.isRental?.toLowerCase() === 'true',
              rentalDetails: row.isRental?.toLowerCase() === 'true' ? {
                startDate: row.rentalStartDate || '',
                endDate: row.rentalEndDate || '',
                cost: parseFloat(row.rentalCost) || 0,
                contactPerson: row.rentalContactPerson || '',
                contactEmail: row.rentalContactEmail || ''
              } : undefined
            };

            addAsset(assetData);
            successCount++;
          } catch (error) {
            errors.push(`Row ${i + 2}: Failed to create asset - ${error}`);
          }
        } else {
          errors.push(`Row ${i + 2}: ${validation.errors.join(', ')}`);
        }
      }

      setImportResults({
        success: successCount,
        errors,
        total: data.length
      });
    } catch (error) {
      setImportResults({
        success: 0,
        errors: [`Failed to parse file: ${error}`],
        total: 0
      });
    } finally {
      setIsProcessing(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const exportToCSV = () => {
    const headers = [
      'name', 'type', 'category', 'serialNumber', 'model', 'manufacturer',
      'purchaseDate', 'purchasePrice', 'currentValue', 'status', 'location',
      'assignedTo', 'description', 'warrantyExpiry', 'lastMaintenance',
      'nextMaintenance', 'vendor', 'isRental', 'rentalStartDate', 'rentalEndDate',
      'rentalCost', 'rentalContactPerson', 'rentalContactEmail'
    ];

    const csvData = assets.map(asset => {
      return headers.map(header => {
        let value = '';
        
        if (header.startsWith('rental') && asset.rentalDetails) {
          const rentalKey = header.replace('rental', '').toLowerCase();
          if (rentalKey === 'startdate') value = asset.rentalDetails.startDate;
          else if (rentalKey === 'enddate') value = asset.rentalDetails.endDate;
          else if (rentalKey === 'cost') value = asset.rentalDetails.cost.toString();
          else if (rentalKey === 'contactperson') value = asset.rentalDetails.contactPerson;
          else if (rentalKey === 'contactemail') value = asset.rentalDetails.contactEmail;
        } else if (header === 'isRental') {
          value = asset.isRental.toString();
        } else {
          value = (asset as any)[header]?.toString() || '';
        }

        // Escape commas and quotes in CSV
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          value = `"${value.replace(/"/g, '""')}"`;
        }
        
        return value;
      }).join(',');
    });

    const csvContent = [headers.join(','), ...csvData].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `assets-export-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'asset-import-template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Bulk Import/Export</h1>
        <p className="text-gray-600 mt-2">Import assets from CSV files or export your asset data</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Import Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-full">
              <Upload className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Import Assets</h2>
              <p className="text-sm text-gray-600">Upload CSV file to add multiple assets</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
              <FileSpreadsheet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                Select a CSV file to import assets
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileImport}
                className="hidden"
                disabled={isProcessing}
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Processing...' : 'Choose File'}
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Import Guidelines:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• CSV file with comma-separated values</li>
                <li>• First row should contain column headers</li>
                <li>• Required fields: name, type, status</li>
                <li>• Use template for proper format</li>
              </ul>
            </div>

            <button
              onClick={downloadTemplate}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Download className="w-4 h-4" />
              Download CSV Template
            </button>
          </div>

          {importResults && (
            <div className="mt-6 p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                {importResults.success === importResults.total ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                )}
                <h4 className="font-medium text-gray-900">Import Results</h4>
              </div>
              
              <div className="text-sm space-y-1">
                <p className="text-green-600">✓ {importResults.success} assets imported successfully</p>
                {importResults.errors.length > 0 && (
                  <p className="text-red-600">✗ {importResults.errors.length} errors occurred</p>
                )}
                <p className="text-gray-600">Total rows processed: {importResults.total}</p>
              </div>

              {importResults.errors.length > 0 && (
                <div className="mt-3 max-h-32 overflow-y-auto">
                  <p className="text-sm font-medium text-red-800 mb-1">Errors:</p>
                  <ul className="text-xs text-red-700 space-y-1">
                    {importResults.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Export Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-green-100 rounded-full">
              <Download className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Export Assets</h2>
              <p className="text-sm text-gray-600">Download your asset data for backup or reporting</p>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <Database className="w-5 h-5 text-gray-600" />
                <h4 className="font-medium text-gray-900">Current Asset Summary</h4>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Assets:</span>
                  <span className="ml-2 font-medium text-gray-900">{assets.length}</span>
                </div>
                <div>
                  <span className="text-gray-600">Active:</span>
                  <span className="ml-2 font-medium text-green-600">
                    {assets.filter(a => a.status === 'Active').length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Owned:</span>
                  <span className="ml-2 font-medium text-blue-600">
                    {assets.filter(a => !a.isRental).length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Rented:</span>
                  <span className="ml-2 font-medium text-orange-600">
                    {assets.filter(a => a.isRental).length}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <button
                onClick={exportToCSV}
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-5 h-5" />
                Export All Assets to CSV
              </button>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-green-800 mb-2">Export Features:</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Complete asset data including rental details</li>
                  <li>• Compatible with Excel and other spreadsheet apps</li>
                  <li>• Includes all custom fields and metadata</li>
                  <li>• Perfect for backup and reporting purposes</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => navigate('/add-asset')}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FileText className="w-4 h-4" />
            Add Single Asset
          </button>
          <button
            onClick={() => navigate('/assets')}
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Database className="w-4 h-4" />
            View All Assets
          </button>
          <button
            onClick={() => navigate('/reports')}
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FileText className="w-4 h-4" />
            Generate Reports
          </button>
        </div>
      </div>
    </div>
  );
};

export default BulkImportExport;