import React, { useState, useEffect } from 'react';
import { useAssets } from '../hooks/useAssets';
import { useAuthContext } from '../components/AuthProvider';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Save, X, Calendar, ShoppingCart } from 'lucide-react';

const EditAsset: React.FC = () => {
  const { assets, updateAsset } = useAssets();
  const { locations, hasPermission, user } = useAuthContext();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const assetId = searchParams.get('id');
  const [selectedAssetId, setSelectedAssetId] = useState(assetId || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    type: 'Hardware' as const,
    category: '',
    serialNumber: '',
    model: '',
    manufacturer: '',
    purchaseDate: '',
    purchasePrice: '',
    currentValue: '',
    status: 'Active' as const,
    location: '',
    assignedTo: '',
    description: '',
    warrantyExpiry: '',
    lastMaintenance: '',
    nextMaintenance: '',
    vendor: '',
    isRental: false,
    rentalStartDate: '',
    rentalEndDate: '',
    rentalCost: '',
    rentalContactPerson: '',
    rentalContactEmail: ''
  });

  // Get active locations for dropdown
  const activeLocations = locations.filter(loc => loc.isActive);

  // Check if user has permission to edit assets
  const canEditAssets = hasPermission('assets', 'update');

  console.log('EditAsset Debug:', {
    user: user?.username,
    canEditAssets,
    assetsCount: assets.length,
    selectedAssetId,
    assetId
  });

export default EditAsset;