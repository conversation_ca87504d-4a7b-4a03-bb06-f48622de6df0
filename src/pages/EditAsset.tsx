import React, { useState, useEffect } from 'react';
import { useAssets } from '../hooks/useAssets';
import { useAuthContext } from '../components/AuthProvider';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Save, X, Calendar, ShoppingCart } from 'lucide-react';

const EditAsset: React.FC = () => {
  const { assets, updateAsset } = useAssets();
  const { locations } = useAuthContext();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const assetId = searchParams.get('id');
  const [selectedAssetId, setSelectedAssetId] = useState(assetId || '');
  
  const [formData, setFormData] = useState({
    name: '',
    type: 'Hardware' as const,
    category: '',
    serialNumber: '',
    model: '',
    manufacturer: '',
    purchaseDate: '',
    purchasePrice: '',
    currentValue: '',
    status: 'Active' as const,
    location: '',
    assignedTo: '',
    description: '',
    warrantyExpiry: '',
    lastMaintenance: '',
    nextMaintenance: '',
    vendor: '',
    isRental: false,
    rentalStartDate: '',
    rentalEndDate: '',
    rentalCost: '',
    rentalContactPerson: '',
    rentalContactEmail: ''
  });

  // Get active locations for dropdown
  const activeLocations = locations.filter(loc => loc.isActive);

  useEffect(() => {
    if (selectedAssetId) {
      const asset = assets.find(a => a.id === selectedAssetId);
      if (asset) {
        setFormData({
          name: asset.name,
          type: asset.type,
          category: asset.category,
          serialNumber: asset.serialNumber,
          model: asset.model,
          manufacturer: asset.manufacturer,
          purchaseDate: asset.purchaseDate,
          purchasePrice: asset.purchasePrice.toString(),
          currentValue: asset.currentValue.toString(),
          status: asset.status,
          location: asset.location,
          assignedTo: asset.assignedTo,
          description: asset.description,
          warrantyExpiry: asset.warrantyExpiry,
          lastMaintenance: asset.lastMaintenance,
          nextMaintenance: asset.nextMaintenance,
          vendor: asset.vendor,
          isRental: asset.isRental,
          rentalStartDate: asset.rentalDetails?.startDate || '',
          rentalEndDate: asset.rentalDetails?.endDate || '',
          rentalCost: asset.rentalDetails?.cost.toString() || '',
          rentalContactPerson: asset.rentalDetails?.contactPerson || '',
          rentalContactEmail: asset.rentalDetails?.contactEmail || ''
        });
      }
    }
  }, [selectedAssetId, assets]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedAssetId) {
      alert('Please select an asset to edit');
      return;
    }

    const updates = {
      name: formData.name,
      type: formData.type,
      category: formData.category,
      serialNumber: formData.serialNumber,
      model: formData.model,
      manufacturer: formData.manufacturer,
      purchaseDate: formData.purchaseDate,
      purchasePrice: parseFloat(formData.purchasePrice) || 0,
      currentValue: parseFloat(formData.currentValue) || 0,
      status: formData.status,
      location: formData.location,
      assignedTo: formData.assignedTo,
      description: formData.description,
      warrantyExpiry: formData.warrantyExpiry,
      lastMaintenance: formData.lastMaintenance,
      nextMaintenance: formData.nextMaintenance,
      vendor: formData.vendor,
      isRental: formData.isRental,
      rentalDetails: formData.isRental ? {
        startDate: formData.rentalStartDate,
        endDate: formData.rentalEndDate,
        cost: parseFloat(formData.rentalCost) || 0,
        contactPerson: formData.rentalContactPerson,
        contactEmail: formData.rentalContactEmail
      } : undefined
    };

    updateAsset(selectedAssetId, updates);
    alert('Asset updated successfully!');
    navigate('/assets');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const activeAssets = assets.filter(asset => asset.status !== 'Retired');

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Edit Asset</h1>
        <p className="text-gray-600 mt-2">Update asset information</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Asset to Edit *</label>
          <select
            value={selectedAssetId}
            onChange={(e) => setSelectedAssetId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select an asset...</option>
            {activeAssets.map((asset) => (
              <option key={asset.id} value={asset.id}>
                {asset.name} - {asset.serialNumber} ({asset.isRental ? 'Rental' : 'Purchased'})
              </option>
            ))}
          </select>
          {activeAssets.length === 0 && (
            <p className="mt-1 text-sm text-red-600">
              No active assets available for editing.
            </p>
          )}
        </div>

        {selectedAssetId && (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Asset Type Indicator */}
            <div className={`p-4 rounded-lg border ${formData.isRental ? 'bg-orange-50 border-orange-200' : 'bg-blue-50 border-blue-200'}`}>
              <div className="flex items-center gap-2">
                {formData.isRental ? (
                  <>
                    <Calendar className="w-5 h-5 text-orange-600" />
                    <span className="font-medium text-orange-800">Rental Asset</span>
                  </>
                ) : (
                  <>
                    <ShoppingCart className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-800">Purchased Asset</span>
                  </>
                )}
              </div>
              <p className="text-sm mt-1 text-gray-600">
                {formData.isRental 
                  ? 'This is a rental asset with vendor-specific details.'
                  : 'This is a company-owned asset with warranty and maintenance tracking.'
                }
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Asset Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Type *</label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Hardware">Hardware</option>
                  <option value="Software">Software</option>
                  <option value="Equipment">Equipment</option>
                  <option value="Furniture">Furniture</option>
                  <option value="Vehicle">Vehicle</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <input
                  type="text"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Serial Number</label>
                <input
                  type="text"
                  name="serialNumber"
                  value={formData.serialNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Model</label>
                <input
                  type="text"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Manufacturer</label>
                <input
                  type="text"
                  name="manufacturer"
                  value={formData.manufacturer}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.isRental ? 'Rental Start Date' : 'Purchase Date'}
                </label>
                <input
                  type="date"
                  name="purchaseDate"
                  value={formData.purchaseDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.isRental ? 'Rental Cost (₹)' : 'Purchase Price (₹)'}
                </label>
                <input
                  type="number"
                  name="purchasePrice"
                  value={formData.purchasePrice}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Current Value (₹)</label>
                <input
                  type="number"
                  name="currentValue"
                  value={formData.currentValue}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                  <option value="Maintenance">Maintenance</option>
                  {formData.isRental && <option value="Checked Out">Checked Out</option>}
                  {!formData.isRental && <option value="Retired">Retired</option>}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Location *</label>
                <select
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a location...</option>
                  {activeLocations.map((location) => (
                    <option key={location.id} value={location.name}>
                      {location.name}
                    </option>
                  ))}
                </select>
                {activeLocations.length === 0 && (
                  <p className="mt-1 text-sm text-red-600">
                    No active locations available. Please contact an administrator.
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
                <input
                  type="text"
                  name="assignedTo"
                  value={formData.assignedTo}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.isRental ? 'Rental Vendor' : 'Vendor'}
                </label>
                <input
                  type="text"
                  name="vendor"
                  value={formData.vendor}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {!formData.isRental && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Warranty Expiry</label>
                    <input
                      type="date"
                      name="warrantyExpiry"
                      value={formData.warrantyExpiry}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Maintenance</label>
                    <input
                      type="date"
                      name="lastMaintenance"
                      value={formData.lastMaintenance}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Next Maintenance</label>
                    <input
                      type="date"
                      name="nextMaintenance"
                      value={formData.nextMaintenance}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Asset Type Toggle */}
            <div className="border-t pt-6">
              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  name="isRental"
                  checked={formData.isRental}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">This is a rental asset</label>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                {formData.isRental 
                  ? 'Rental assets have vendor contact details and rental periods instead of warranty information.'
                  : 'Purchased assets have warranty and maintenance tracking instead of rental details.'
                }
              </p>
            </div>

            {/* Rental Details Section */}
            {formData.isRental && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Rental Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Rental End Date</label>
                    <input
                      type="date"
                      name="rentalEndDate"
                      value={formData.rentalEndDate}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Rental Cost (₹)</label>
                    <input
                      type="number"
                      name="rentalCost"
                      value={formData.rentalCost}
                      onChange={handleChange}
                      step="0.01"
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Person</label>
                    <input
                      type="text"
                      name="rentalContactPerson"
                      value={formData.rentalContactPerson}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                    <input
                      type="email"
                      name="rentalContactEmail"
                      value={formData.rentalContactEmail}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Calendar className="w-4 h-4 text-orange-600 mt-0.5" />
                    <div className="text-sm text-orange-800">
                      <p className="font-medium">Rental Asset Information:</p>
                      <p>These details help track rental periods, vendor contacts, and rental costs for proper asset management.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex gap-4 pt-6 border-t">
              <button
                type="submit"
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                Update Asset
              </button>
              <button
                type="button"
                onClick={() => navigate('/assets')}
                className="flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <X className="w-4 h-4" />
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default EditAsset;