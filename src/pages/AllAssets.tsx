import React, { useState, useMemo } from 'react';
import { useAssets } from '../hooks/useAssets';
import { useAuthContext } from '../components/AuthProvider';
import { Eye, Edit, Trash2, Calendar, ShoppingCart, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import AdvancedSearch, { SearchFilters } from '../components/AdvancedSearch';
import AssetHistoryModal from '../components/AssetHistoryModal';

const AllAssets: React.FC = () => {
  const { assets } = useAssets();
  const { locations } = useAuthContext();
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: '',
    statusFilter: 'All',
    typeFilter: 'All',
    acquisitionFilter: 'All',
    vendorFilter: 'All',
    modelFilter: 'All',
    assignedToFilter: 'All',
    locationFilter: 'All',
    specificationsSearch: '',
    dateField: 'purchaseDate',
    dateRangeStart: '',
    dateRangeEnd: ''
  });
  const [historyModal, setHistoryModal] = useState<{ isOpen: boolean; assetId: string; assetName: string }>({
    isOpen: false,
    assetId: '',
    assetName: ''
  });

  // Extract unique values for filter dropdowns
  const filterOptions = useMemo(() => {
    const vendors = [...new Set(assets.map(asset => asset.vendor).filter(Boolean))].sort();
    const models = [...new Set(assets.map(asset => asset.model).filter(Boolean))].sort();
    const assignedUsers = [...new Set(assets.map(asset => asset.assignedTo).filter(Boolean))].sort();
    const assetLocations = [...new Set(assets.map(asset => asset.location).filter(Boolean))].sort();
    
    return { vendors, models, assignedUsers, locations: assetLocations };
  }, [assets]);

  const filteredAssets = useMemo(() => {
    return assets.filter(asset => {
      // Basic search term (name, serial number, model)
      const matchesSearch = !filters.searchTerm || 
        asset.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        asset.serialNumber.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        asset.model.toLowerCase().includes(filters.searchTerm.toLowerCase());

      // Status filter
      const matchesStatus = filters.statusFilter === 'All' || asset.status === filters.statusFilter;

      // Type filter
      const matchesType = filters.typeFilter === 'All' || asset.type === filters.typeFilter;

      // Acquisition filter
      const matchesAcquisition = filters.acquisitionFilter === 'All' || 
        (filters.acquisitionFilter === 'Purchased' && !asset.isRental) ||
        (filters.acquisitionFilter === 'Rental' && asset.isRental);

      // Vendor filter
      const matchesVendor = filters.vendorFilter === 'All' || asset.vendor === filters.vendorFilter;

      // Model filter
      const matchesModel = filters.modelFilter === 'All' || asset.model === filters.modelFilter;

      // Assigned user filter
      const matchesAssignedTo = filters.assignedToFilter === 'All' || asset.assignedTo === filters.assignedToFilter;

      // Location filter
      const matchesLocation = filters.locationFilter === 'All' || asset.location === filters.locationFilter;

      // Specifications search (full-text search on multiple fields)
      const matchesSpecifications = !filters.specificationsSearch || [
        asset.description,
        asset.category,
        asset.manufacturer,
        asset.name,
        asset.model,
        asset.serialNumber
      ].some(field => 
        field && field.toLowerCase().includes(filters.specificationsSearch.toLowerCase())
      );

      // Date range filter
      let matchesDateRange = true;
      if (filters.dateRangeStart || filters.dateRangeEnd) {
        const assetDate = new Date((asset as any)[filters.dateField]);
        const startDate = filters.dateRangeStart ? new Date(filters.dateRangeStart) : null;
        const endDate = filters.dateRangeEnd ? new Date(filters.dateRangeEnd) : null;

        if (startDate && assetDate < startDate) {
          matchesDateRange = false;
        }
        if (endDate && assetDate > endDate) {
          matchesDateRange = false;
        }
      }

      return matchesSearch && 
             matchesStatus && 
             matchesType && 
             matchesAcquisition && 
             matchesVendor && 
             matchesModel && 
             matchesAssignedTo && 
             matchesLocation &&
             matchesSpecifications && 
             matchesDateRange;
    });
  }, [assets, filters]);

  const statusColors = {
    'Active': 'bg-green-100 text-green-800',
    'Inactive': 'bg-gray-100 text-gray-800',
    'Maintenance': 'bg-yellow-100 text-yellow-800',
    'Retired': 'bg-red-100 text-red-800',
    'Checked Out': 'bg-orange-100 text-orange-800'
  };

  const openHistoryModal = (assetId: string, assetName: string) => {
    setHistoryModal({ isOpen: true, assetId, assetName });
  };

  const closeHistoryModal = () => {
    setHistoryModal({ isOpen: false, assetId: '', assetName: '' });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">All Assets</h1>
        <p className="text-gray-600 mt-2">
          Manage and view all your IT assets with advanced search and filtering
        </p>
      </div>

      {/* Advanced Search Component */}
      <AdvancedSearch
        onFiltersChange={setFilters}
        vendors={filterOptions.vendors}
        models={filterOptions.models}
        assignedUsers={filterOptions.assignedUsers}
        locations={filterOptions.locations}
      />

      {/* Results Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="text-sm text-blue-800">
              <span className="font-semibold">{filteredAssets.length}</span> of{' '}
              <span className="font-semibold">{assets.length}</span> assets found
            </div>
          </div>
          {filteredAssets.length !== assets.length && (
            <div className="text-xs text-blue-600">
              Filters applied - showing filtered results
            </div>
          )}
        </div>
      </div>

      {/* Assets Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Asset</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Type</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Acquisition</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Vendor</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Serial Number</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Assigned To</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Location</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Value</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredAssets.map((asset) => (
                <tr key={asset.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900">{asset.name}</p>
                      <p className="text-sm text-gray-600">{asset.model}</p>
                      {asset.category && (
                        <p className="text-xs text-gray-500">{asset.category}</p>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      {asset.type}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-1">
                      {asset.isRental ? (
                        <>
                          <Calendar className="w-4 h-4 text-orange-600" />
                          <span className="text-sm text-orange-700 font-medium">Rental</span>
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-700 font-medium">Purchased</span>
                        </>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-900">{asset.vendor || '-'}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-gray-900 font-mono text-sm">{asset.serialNumber || '-'}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${statusColors[asset.status]}`}>
                      {asset.status}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-900">{asset.assignedTo || '-'}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-900">{asset.location || '-'}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-gray-900 font-medium">
                      ₹{asset.currentValue.toLocaleString('en-IN')}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex gap-2">
                      <button 
                        onClick={() => openHistoryModal(asset.id, asset.name)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View History"
                      >
                        <Clock className="w-4 h-4" />
                      </button>
                      <button 
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <Link 
                        to={`/edit-asset?id=${asset.id}`}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Asset"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                      {/* Only show retire option for purchased assets */}
                      {!asset.isRental && (
                        <Link 
                          to={`/retire-asset?id=${asset.id}`}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Retire Asset"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Link>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredAssets.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Eye className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No assets found</h3>
            <p className="text-gray-600 mb-4">
              No assets match your current search criteria.
            </p>
            <button
              onClick={() => setFilters({
                searchTerm: '',
                statusFilter: 'All',
                typeFilter: 'All',
                acquisitionFilter: 'All',
                vendorFilter: 'All',
                modelFilter: 'All',
                assignedToFilter: 'All',
                locationFilter: 'All',
                specificationsSearch: '',
                dateField: 'purchaseDate',
                dateRangeStart: '',
                dateRangeEnd: ''
              })}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>

      {/* Export Options */}
      {filteredAssets.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Export filtered results ({filteredAssets.length} assets)
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  const csvData = filteredAssets.map(asset => ({
                    Name: asset.name,
                    Type: asset.type,
                    Model: asset.model,
                    'Serial Number': asset.serialNumber,
                    Vendor: asset.vendor,
                    Status: asset.status,
                    'Assigned To': asset.assignedTo,
                    Location: asset.location,
                    'Current Value': asset.currentValue,
                    'Purchase Date': asset.purchaseDate
                  }));
                  
                  const headers = Object.keys(csvData[0]);
                  const csvContent = [
                    headers.join(','),
                    ...csvData.map(row => headers.map(header => 
                      JSON.stringify(row[header as keyof typeof row] || '')
                    ).join(','))
                  ].join('\n');

                  const blob = new Blob([csvContent], { type: 'text/csv' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `filtered-assets-${new Date().toISOString().split('T')[0]}.csv`;
                  a.click();
                  window.URL.revokeObjectURL(url);
                }}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Export CSV
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Asset History Modal */}
      <AssetHistoryModal
        assetId={historyModal.assetId}
        assetName={historyModal.assetName}
        isOpen={historyModal.isOpen}
        onClose={closeHistoryModal}
      />
    </div>
  );
};

export default AllAssets;