import React, { useState } from 'react';
import { useAssets } from '../hooks/useAssets';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Trash2, AlertTriangle, X, ShoppingCart } from 'lucide-react';

const RetireAsset: React.FC = () => {
  const { assets, retireAsset } = useAssets();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const assetId = searchParams.get('id');
  const [selectedAssetId, setSelectedAssetId] = useState(assetId || '');
  const [reason, setReason] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleRetire = () => {
    if (!selectedAssetId) return;
    
    retireAsset(selectedAssetId);
    navigate('/assets');
  };

  // Only show purchased assets for retirement (rental assets cannot be retired)
  const purchasedAssets = assets.filter(asset => 
    !asset.isRental && asset.status !== 'Retired'
  );
  const selectedAsset = assets.find(asset => asset.id === selectedAssetId);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Retire Asset</h1>
        <p className="text-gray-600 mt-2">Mark purchased assets as retired when they are no longer in use</p>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <ShoppingCart className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-800">Purchased Assets Only</h4>
            <p className="text-sm text-blue-700 mt-1">
              Only purchased assets can be retired. Rental assets should be returned to the vendor instead of being retired.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Purchased Asset to Retire</label>
          <select
            value={selectedAssetId}
            onChange={(e) => setSelectedAssetId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a purchased asset...</option>
            {purchasedAssets.map((asset) => (
              <option key={asset.id} value={asset.id}>
                {asset.name} - {asset.serialNumber} - {asset.status}
              </option>
            ))}
          </select>
          
          {purchasedAssets.length === 0 && (
            <div className="mt-4 text-center py-8">
              <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No purchased assets available for retirement</p>
              <p className="text-sm text-gray-400">
                All purchased assets are already retired or no purchased assets exist.
              </p>
            </div>
          )}
        </div>

        {selectedAsset && (
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <ShoppingCart className="w-4 h-4" />
                Purchased Asset Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>
                  <span className="ml-2 text-gray-900">{selectedAsset.name}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Serial Number:</span>
                  <span className="ml-2 text-gray-900 font-mono">{selectedAsset.serialNumber}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Model:</span>
                  <span className="ml-2 text-gray-900">{selectedAsset.model}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Current Value:</span>
                  <span className="ml-2 text-gray-900">₹{selectedAsset.currentValue.toLocaleString('en-IN')}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Location:</span>
                  <span className="ml-2 text-gray-900">{selectedAsset.location}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Assigned To:</span>
                  <span className="ml-2 text-gray-900">{selectedAsset.assignedTo}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Purchase Date:</span>
                  <span className="ml-2 text-gray-900">{new Date(selectedAsset.purchaseDate).toLocaleDateString()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Asset Type:</span>
                  <span className="ml-2 text-gray-900">Purchased Asset</span>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Reason for Retirement</label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={4}
                placeholder="Please provide a reason for retiring this asset (e.g., end of life, broken beyond repair, obsolete, etc.)"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Warning</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Retiring this purchased asset will mark it as no longer active. This action can be reversed by editing the asset later.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex gap-4 pt-6 border-t">
              <button
                onClick={() => setShowConfirmation(true)}
                className="flex items-center gap-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Retire Asset
              </button>
              <button
                onClick={() => navigate('/assets')}
                className="flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <X className="w-4 h-4" />
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>

      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Confirm Asset Retirement</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to retire "{selectedAsset?.name}"? This will change its status to "Retired".
            </p>
            
            <div className="flex gap-4">
              <button
                onClick={handleRetire}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Yes, Retire Asset
              </button>
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RetireAsset;