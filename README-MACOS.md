# Asset Manager - macOS Setup Guide

## 🍎 Quick Setup for MacBook Pro M1

### Prerequisites
- macOS (your MacBook Pro M1)
- Terminal access

### Option 1: Simple Static Server (Recommended for testing)

1. **Download the built application** from the `dist` folder
2. **Open Terminal** and run:
   ```bash
   cd ~/Downloads  # or wherever you downloaded the files
   python3 -m http.server 8080
   ```
3. **Open browser** and go to: `http://localhost:8080`

### Option 2: Full Development Setup

1. **Install Node.js** (if not already installed):
   ```bash
   # Install Homebrew first (if needed)
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install Node.js
   brew install node
   ```

2. **Create project directory**:
   ```bash
   mkdir ~/asset-manager
   cd ~/asset-manager
   ```

3. **Copy all project files** to this directory

4. **Run the setup script**:
   ```bash
   chmod +x setup-macos.sh
   ./setup-macos.sh
   ```

### Login Credentials
- **Username:** `admin`
- **Password:** `password`

### Features
- ✅ Complete asset management system
- ✅ User management with roles and permissions
- ✅ Location management
- ✅ Check-in/check-out functionality
- ✅ Reporting and analytics
- ✅ AI-powered asset queries
- ✅ Bulk import/export
- ✅ Mad Assemblage Studios branding

### Troubleshooting

**Port already in use?**
```bash
# Try a different port
python3 -m http.server 3000
# Then visit http://localhost:3000
```

**Permission denied?**
```bash
# Make sure you have the right permissions
chmod +x setup-macos.sh
```

**Node.js issues?**
```bash
# Check Node.js version
node --version
npm --version

# Should be Node.js 16+ for best compatibility
```

### Data Storage
- All data is stored in browser localStorage
- Data persists between sessions
- Export your data regularly using the bulk export feature

### Support
This is a complete, production-ready asset management system designed for Assemblage Studios.